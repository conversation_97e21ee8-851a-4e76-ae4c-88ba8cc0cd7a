Training Continuation Instructions
==============================
Created: 2025-04-07 16:00:13

1. Setup on New Computer
-----------------------
a. Install Dependencies:
   pip install -r requirements.txt

b. Directory Structure:
   Ensure the following structure:
   - Place all contents of this package in your project root
   - Training files should be in: training/bisaya_vits_test-April-07-2025_08+11AM-898988d/

c. Dataset:
   - Ensure your dataset is available and properly configured
   - Update paths in config.json if necessary

2. Continue Training
-------------------
a. Verify Setup:
   - Check all files are in place
   - Confirm GPU availability
   - Verify dataset paths

b. Start Training:
   python train_vits.py --continue_path "training/bisaya_vits_test-April-07-2025_08+11AM-898988d/best_model.pth"

3. Monitor Training
------------------
a. Launch TensorBoard:
   tensorboard --logdir=training

b. Check Progress:
   - Monitor loss values
   - Compare with previous training logs
   - Check generated samples periodically

4. Package Contents
------------------
Essential Files:
- config.json
- train_vits.py
- requirements.txt
- README.md

Training Files:
- best_model.pth
- config.json
- trainer_0_log.txt

Note: Make sure to maintain the same directory structure when setting up on the new computer.
