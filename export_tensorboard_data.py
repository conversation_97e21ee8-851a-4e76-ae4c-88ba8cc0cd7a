import os
import pandas as pd
import tensorflow as tf
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

def export_tensorboard_data(log_dir, output_dir):
    """
    Export TensorBoard data to CSV files
    
    Args:
        log_dir: Directory containing TensorBoard event files
        output_dir: Directory to save CSV files
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all event files in the log directory
    event_files = [f for f in os.listdir(log_dir) if f.startswith('events.out.tfevents')]
    
    # Process each event file
    for event_file in event_files:
        event_path = os.path.join(log_dir, event_file)
        print(f"Processing {event_file}...")
        
        # Load the event file
        ea = EventAccumulator(event_path)
        ea.Reload()
        
        # Get all tags (metrics) in the event file
        tags = ea.Tags()['scalars']
        
        # Export each metric to a CSV file
        for tag in tags:
            # Get all events for this tag
            events = ea.Scalars(tag)
            
            # Convert to DataFrame
            data = []
            for event in events:
                data.append({
                    'step': event.step,
                    'value': event.value,
                    'wall_time': event.wall_time
                })
            
            df = pd.DataFrame(data)
            
            # Save to CSV
            # Replace '/' with '_' in tag name for filename
            filename = f"{tag.replace('/', '_')}.csv"
            output_path = os.path.join(output_dir, filename)
            df.to_csv(output_path, index=False)
            print(f"Exported {tag} to {output_path}")

if __name__ == "__main__":
    # Directory containing TensorBoard logs
    log_dir = "training/bisaya_vits/bisaya_vits-April-10-2025_01+57AM-10597ee"
    
    # Directory to save CSV files
    output_dir = "tensorboard_data"
    
    # Export data
    export_tensorboard_data(log_dir, output_dir)
    print("Export complete!") 