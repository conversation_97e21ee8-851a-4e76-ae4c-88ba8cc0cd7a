{"cells": [{"cell_type": "markdown", "metadata": {"id": "header_title"}, "source": ["# Bisaya TTS - VITS Training on Google Colab\n", "\n", "This notebook is designed to train a VITS TTS model for the Bisaya language using the Bloom Cebuano dataset."]}, {"cell_type": "markdown", "metadata": {"id": "section_setup"}, "source": ["## 1. Setup Environment\n", "\n", "First, let's install the required dependencies and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "check_gpu"}, "outputs": [], "source": ["# Check if GPU is available\n", "!nvidia-smi"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_requirements"}, "outputs": [], "source": ["# Install TTS and other dependencies\n", "!pip install TTS\n", "!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {"id": "section_data"}, "source": ["## 2. Upload or Download your Dataset\n", "\n", "You can either upload your dataset to Colab or use the provided code to download it from Google Drive or other sources."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Copy your dataset from Google Drive (adjust paths as needed)\n", "!mkdir -p /content/data\n", "!cp -r /content/drive/MyDrive/path_to_your_dataset/ /content/data/\n", "\n", "# Or upload directly to Colab (use the file upload button in the left sidebar)\n", "# Then unzip if needed\n", "# !unzip /content/uploaded_dataset.zip -d /content/data/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Alternative: Download dataset from a shared URL\n", "\n", "If you have your dataset hosted somewhere, you can download it directly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Download dataset from a URL\n", "# !wget -O dataset.zip https://example.com/your_dataset.zip\n", "# !unzip dataset.zip -d /content/data/"]}, {"cell_type": "markdown", "metadata": {"id": "section_prepare"}, "source": ["## 3. Prepare Data Structure\n", "\n", "Let's set up the proper directory structure and file formats for training."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "prepare_data"}, "outputs": [], "source": ["import os\n", "import shutil\n", "\n", "# Define dataset path\n", "dataset_path = \"/content/data/bloom_ceb_dataset/train\"\n", "\n", "# Create wavs directory if it doesn't exist\n", "wavs_dir = os.path.join(dataset_path, \"wavs\")\n", "os.makedirs(wavs_dir, exist_ok=True)\n", "\n", "# Copy audio files from audio directory to wavs directory\n", "audio_dir = os.path.join(dataset_path, \"audio\")\n", "if os.path.exists(audio_dir):\n", "    for file in os.listdir(audio_dir):\n", "        if file.endswith(\".wav\"):\n", "            src = os.path.join(audio_dir, file)\n", "            dst = os.path.join(wavs_dir, file)\n", "            if not os.path.exists(dst):\n", "                shutil.copy2(src, dst)\n", "    print(f\"Copied audio files to {wavs_dir}\")\n", "\n", "# Convert transcripts to LJSpeech format\n", "transcripts_path = os.path.join(dataset_path, \"transcripts.txt\")\n", "output_path = os.path.join(dataset_path, \"metadata_ljspeech.csv\")\n", "\n", "# Read the tab-separated file\n", "with open(transcripts_path, 'r', encoding='utf-8') as f:\n", "    lines = f.readlines()\n", "\n", "# Convert to LJSpeech format (filename|transcription|normalized_transcription)\n", "with open(output_path, 'w', encoding='utf-8') as f:\n", "    for line in lines:\n", "        parts = line.strip().split('\\t')\n", "        if len(parts) >= 2:\n", "            filename = parts[0].replace('.wav', '')\n", "            transcription = parts[1]\n", "            # Use the same text for normalized transcription\n", "            f.write(f\"{filename}|{transcription}|{transcription}\\n\")\n", "\n", "print(f\"Converted transcripts to LJSpeech format: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "section_train"}, "source": ["## 4. Start Training\n", "\n", "Now we can run the training script with our prepared dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_model"}, "outputs": [], "source": ["# Copy train_vits.py to the current directory if needed\n", "# !cp /content/drive/MyDrive/path_to_scripts/train_vits.py .\n", "\n", "# Run the training script\n", "!python train_vits.py"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Monitor Training Progress\n", "\n", "You can monitor the training progress using TensorBoard."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext tensorboard\n", "%tensorboard --logdir=/content/training"]}, {"cell_type": "markdown", "metadata": {"id": "section_save"}, "source": ["## 6. Save Trained Model\n", "\n", "After training is completed, save the model files back to Google Drive or download them."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_model"}, "outputs": [], "source": ["# Compress the training outputs\n", "!tar -czf bisaya_vits_model.tar.gz training/\n", "\n", "# Copy to Google Drive\n", "!cp bisaya_vits_model.tar.gz /content/drive/MyDrive/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Test the Trained Model\n", "\n", "Let's test our trained model to synthesize some Bisaya speech."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Audio\n", "from TTS.utils.synthesizer import Synthesizer\n", "\n", "# Find the best checkpoint\n", "import glob\n", "import os\n", "\n", "# Locate the latest checkpoint\n", "model_path = \"/content/training/bisaya_vits_bloom\"\n", "checkpoint_dir = sorted(glob.glob(f\"{model_path}/*\"))[0]\n", "checkpoint_file = sorted(glob.glob(f\"{checkpoint_dir}/checkpoint_*.pth\"))[-1]\n", "config_file = os.path.join(checkpoint_dir, \"config.json\")\n", "\n", "print(f\"Using checkpoint: {checkpoint_file}\")\n", "print(f\"Config file: {config_file}\")\n", "\n", "# Initialize the synthesizer\n", "synthesizer = Synthesizer(\n", "    tts_checkpoint=checkpoint_file,\n", "    tts_config_path=config_file,\n", "    use_cuda=True\n", ")\n", "\n", "# Synthesize speech\n", "text = \"<PERSON><PERSON><PERSON> ka\"\n", "outputs = synthesizer.tts(text)\n", "wavs = outputs[\"wav\"]\n", "sample_rate = outputs[\"sample_rate\"]\n", "\n", "# Play the audio\n", "Audio(wavs, rate=sample_rate)"]}], "metadata": {"colab": {"private_outputs": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 0}