# TTS Datasets

Some of the known public datasets that we successfully applied 🐸TTS:

- [English - LJ Speech](https://keithito.com/LJ-Speech-Dataset/)
- [English - Nancy](http://www.cstr.ed.ac.uk/projects/blizzard/2011/lessac_blizzard2011/)
- [English - TWEB](https://www.kaggle.com/bryanpark/the-world-english-bible-speech-dataset)
- [English - LibriTTS](https://openslr.org/60/)
- [English - VCTK](https://datashare.ed.ac.uk/handle/10283/2950)
- [Multilingual - M-AI-Labs](http://www.caito.de/2019/01/the-m-ailabs-speech-dataset/)
- [Spanish](https://drive.google.com/file/d/1Sm_zyBo67XHkiFhcRSQ4YaHPYM0slO_e/view?usp=sharing) - thx! @carlfm01
- [German - Thorsten OGVD](https://github.com/thorstenMueller/deep-learning-german-tts)
- [Japanese - Kokoro](https://www.kaggle.com/kaiida/kokoro-speech-dataset-v11-small/version/1)
- [Chinese](https://www.data-baker.com/data/index/source/)
- [Ukrainian - LADA](https://github.com/egorsmkv/ukrainian-tts-datasets/tree/main/lada)

Let us know if you use 🐸TTS on a different dataset.
