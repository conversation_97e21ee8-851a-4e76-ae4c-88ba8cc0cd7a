import os
import librosa
import pandas as pd
from pathlib import Path
import re

def clean_text(filename):
    # Remove file extension
    base_name = os.path.splitext(filename)[0]
    
    # Split by underscore
    parts = base_name.split('_')
    
    # Get only the transcription parts (skip ID, augmentation type, and values)
    if len(parts) >= 2:
        # Get all parts except first (ID) and last two (augmentation type and value)
        transcription_parts = parts[1:-2]
        # Join the transcription parts back together
        return ' '.join(transcription_parts)
    return base_name

def get_file_id(filename):
    # Extract the number before the first underscore
    parts = filename.split('_')
    if parts:
        # Remove any non-digit characters from the first part
        numbers = re.findall(r'\d+', parts[0])
        return int(numbers[0]) if numbers else 0
    return 0

def generate_metadata(audio_dir="data/wavs_augmented", output_file="metadata.csv"):
    # Create output directory if it doesn't exist
    os.makedirs(audio_dir, exist_ok=True)
    
    # Get all wav files
    audio_files = list(Path(audio_dir).glob("*.wav"))
    
    if not audio_files:
        print(f"No .wav files found in {audio_dir}")
        return
    
    # Initialize data list
    data = []
    
    # Process each audio file
    for audio_file in audio_files:
        try:
            # Get duration using librosa
            duration = librosa.get_duration(path=str(audio_file))
            
            # Create entry
            entry = {
                'file_id': get_file_id(audio_file.name),
                'file_name': audio_file.name,
                'text': clean_text(audio_file.name),
                'speaker_name': 'speaker1',  # Update this as needed
                'file_path': str(audio_file),
                'duration': round(duration, 2)
            }
            data.append(entry)
            print(f"Processed: {audio_file.name} -> Text: {entry['text']}")  # Debug line
            
        except Exception as e:
            print(f"Error processing {audio_file}: {str(e)}")
    
    # Sort by file_id
    data.sort(key=lambda x: x['file_id'])
    
    # Create DataFrame and save to CSV
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False)
    print(f"Generated metadata.csv with {len(data)} entries")
    print("Text content extracted (Bisaya only, augmentation words removed)")

if __name__ == "__main__":
    generate_metadata() 