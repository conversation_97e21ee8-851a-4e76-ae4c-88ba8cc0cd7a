import os
import re

# Folder containing the files
folder_path = "data/wavs_final"

# Regular expression to match file names
pattern = re.compile(r"^(\d+_[^_]*)((?:_[a-z]+[-\d\.]*)+)?(_\d+)?\.wav$")

# Function to clean up duplicate augmentation tags
def clean_filename(filename):
    match = pattern.match(filename)
    if not match:
        return None  # Skip if it doesn't match the expected pattern

    base_name, augments, speaker_id = match.groups()

    if augments:
        augment_parts = augments.split("_")[1:]  # Remove empty first split
        cleaned_augments = []
        seen = set()

        for aug in augment_parts:
            if aug not in seen:
                cleaned_augments.append(aug)
                seen.add(aug)

        augments = "_" + "_".join(cleaned_augments) if cleaned_augments else ""
    else:
        augments = ""

    new_filename = f"{base_name}{augments}{speaker_id or ''}.wav"
    return new_filename

# Rename files in the folder
for filename in os.listdir(folder_path):
    if filename.endswith(".wav"):
        new_filename = clean_filename(filename)
        if new_filename and new_filename != filename:
            old_path = os.path.join(folder_path, filename)
            new_path = os.path.join(folder_path, new_filename)

            # Ensure we are not overwriting an existing file
            counter = 1
            while os.path.exists(new_path):
                new_filename = f"{os.path.splitext(new_filename)[0]}_{counter}.wav"
                new_path = os.path.join(folder_path, new_filename)
                counter += 1

            os.rename(old_path, new_path)
            print(f"Renamed: {filename} -> {new_filename}")

print("✅ File renaming complete!")
