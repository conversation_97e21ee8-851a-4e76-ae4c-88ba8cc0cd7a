# Bisaya Text-to-Speech (TTS) System

A text-to-speech system for the Bisaya language using VITS (Conditional Variational Autoencoder with Adversarial Learning).

## Setup and Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/Bisaya_TTS.git
cd Bisaya_TTS
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Training the Model

1. Prepare your environment:
   - Ensure your dataset is in the correct format
   - Verify config.json settings match your dataset

2. Start training:
```bash
python train_vits.py
```

Training parameters:
- Default epochs: 10
- Steps per epoch: 308
- Batch size: 32 (configurable in config.json)

To stop training safely:
- Press Ctrl+C once for graceful shutdown
- Wait for the current step to complete
- Model will save checkpoint before stopping

## Monitoring Training with TensorBoard

1. Launch TensorBoard:
```bash
tensorboard --logdir=training/bisaya_vits/[your-training-folder]
```
Replace [your-training-folder] with your actual training folder name (format: bisaya_vits_test-[date]-[hash])

2. Access TensorBoard:
- Open your web browser
- Go to http://localhost:6006
- Monitor these key metrics:
  * Generator Loss
  * Discriminator Loss
  * Mel-Spectrogram Loss

3. Saving TensorBoard Visualizations:
- Hover over any graph
- Click the download icon (⋮) in the top-right corner
- Select "Download PNG"
- Or use right-click → "Save image as..."

## Testing the Model

1. Generate test audio:
```bash
tts --model_path training/bisaya_vits/[checkpoint_folder]/checkpoint_[number].pth --config_path config.json --text "Your Bisaya text here"
```

2. Test outputs will be saved in the `test_outputs` directory

## Training Progress and Results

Current training metrics (as of April 07, 2025):
- Generator Loss: 4.58 → 3.45 (24.72% improvement)
- Discriminator Loss: 6.04 → 1.84 (69.51% improvement)
- Mel-Spectrogram Loss: 108.71 → 36.64 (66.30% improvement)

For detailed analysis:
- Check `results/training_observations.txt`
- View loss curves in `training_metrics.png`
- See full progress report in `progress_report_04_07_2025.txt`

## Directory Structure

```
Bisaya_TTS/
├── config.json           # Model configuration
├── train_vits.py        # Training script
├── test_vits.py         # Testing script
├── requirements.txt     # Dependencies
├── results/            # Analysis and metrics
└── training/           # Training checkpoints and logs
```

## Notes

- Training requires significant computational resources
- Regular checkpoints are saved in the training directory
- Monitor TensorBoard metrics for training progress
- More epochs may be needed for better audio quality
