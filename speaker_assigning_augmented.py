import os
import csv
import shutil
from pathlib import Path

# Configuration
original_metadata = "data/metadata_combined_speakers.csv"
audio_dir = "data/wavs_augmented"
output_dir = "data/wavs_organized"
splits = ["train", "test", "val"]

def normalize_text(text):
    """Normalize text by removing special characters and extra spaces."""
    return " ".join(text.replace("_", " ").split())

def extract_base_text(filename):
    """Extract base text from filename by removing augmentation suffixes."""
    # Remove augmentation suffixes like _pitch_3, _speed_0.8, etc.
    if "_pitch_" in filename:
        filename = filename.split("_pitch_")[0]
    elif "_speed_" in filename:
        filename = filename.split("_speed_")[0]
    elif "_time_" in filename:
        filename = filename.split("_time_")[0]
    return filename

def process_files():
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # Create speaker mapping from original metadata
    speaker_map = {}
    with open(original_metadata, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Normalize transcription text for matching
            base_text = normalize_text(row["Transcription Text"])
            speaker_map[base_text] = row["Bisaya Speaker No."]
    
    # Debug: Print speaker map
    print("Speaker Map:", speaker_map)

    # Rename files and collect all files
    all_files = []
    for file in Path(audio_dir).glob("*.wav"):
        parts = file.stem.split("_")
        original_id = parts[0]
        text_parts = parts[1:-1] if "augmentation" in file.stem else parts[1:]
        augmentation = ""
        
        # Extract augmentation type
        if "pitch" in file.stem:
            augmentation = f"pitch_{parts[-2]}_{parts[-1]}"  # Fixed: Removed "augmentation_" prefix
        elif "speed" in file.stem:
            augmentation = f"speed_{parts[-1]}"  # Fixed: Removed "augmentation_" prefix
        elif "time" in file.stem:
            augmentation = f"time_{parts[-1]}"  # Fixed: Removed "augmentation_" prefix

        # Extract base text by removing augmentation suffixes
        base_text = extract_base_text("_".join(text_parts))
        base_text = normalize_text(base_text)  # Normalize for matching
        speaker_id = speaker_map.get(base_text, "unknown")
        
        # Debug: Print base text and speaker ID
        print(f"Base Text: {base_text}, Speaker ID: {speaker_id}")

        # Create new filename
        new_name = f"{original_id}_{'_'.join(text_parts)}_{augmentation}_{speaker_id}.wav" if augmentation else f"{original_id}_{'_'.join(text_parts)}_{speaker_id}.wav"
        
        # Copy file to output directory with new name
        new_path = Path(output_dir) / new_name
        shutil.copy(file, new_path)
        
        all_files.append({
            "old_path": str(file),
            "new_name": new_name,
            "speaker": speaker_id,
            "text": "_".join(text_parts),
            "augmentation": augmentation
        })
        print("New Name:", new_name)

    return all_files

# First run to rename files
file_info = process_files()