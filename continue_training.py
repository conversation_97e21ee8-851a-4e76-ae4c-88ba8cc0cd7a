import os
import argparse
import torch

from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.vits import Vits, VitsAudioConfig
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor
from trainer import Trainer, TrainerArgs

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Continue training VITS model from checkpoint')
    parser.add_argument('--checkpoint_dir', type=str, required=True, 
                        help='Directory containing the checkpoint (e.g., training/bisaya_vits/bisaya_vits-April-10-2025_01+57AM-10597ee)')
    parser.add_argument('--checkpoint_file', type=str, default=None,
                        help='Specific checkpoint file to load (e.g., checkpoint_30000.pth). If not specified, will use the latest checkpoint.')
    args = parser.parse_args()

    # Check for available GPU
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Define paths
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "training/bisaya_vits")
    dataset_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data/wavs_augmented")
    phoneme_cache_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "phoneme_cache")

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    os.makedirs(phoneme_cache_path, exist_ok=True)

    # Find the checkpoint file
    checkpoint_path = None
    if args.checkpoint_file:
        # Use the specified checkpoint file
        checkpoint_path = os.path.join(args.checkpoint_dir, args.checkpoint_file)
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
    else:
        # Find the latest checkpoint in the directory
        checkpoint_files = [f for f in os.listdir(args.checkpoint_dir) if f.startswith("checkpoint_") and f.endswith(".pth")]
        if not checkpoint_files:
            raise FileNotFoundError(f"No checkpoint files found in {args.checkpoint_dir}")
        
        # Sort by step number (extracted from filename)
        checkpoint_files.sort(key=lambda x: int(x.split("_")[1].split(".")[0]))
        checkpoint_path = os.path.join(args.checkpoint_dir, checkpoint_files[-1])
    
    print(f"Loading checkpoint from: {checkpoint_path}")

    # Define dataset config
    dataset_config = BaseDatasetConfig(
        formatter="ljspeech", 
        meta_file_train="metadata_ljspeech.csv", 
        path=dataset_path
    )

    # Define audio config
    audio_config = VitsAudioConfig(
        sample_rate=22050, 
        win_length=1024, 
        hop_length=256, 
        num_mels=80, 
        mel_fmin=0, 
        mel_fmax=None
    )

    # Define VITS configuration
    config = VitsConfig(
        audio=audio_config,
        run_name="bisaya_vits",
        run_description="VITS training for Bisaya language",
        batch_size=16 if device == "cuda" else 4,  # Larger batch size for GPU
        eval_batch_size=8 if device == "cuda" else 2,
        batch_group_size=3,
        num_loader_workers=4 if device == "cuda" else 2,
        num_eval_loader_workers=2 if device == "cuda" else 1,
        run_eval=True,
        test_delay_epochs=5,
        epochs=100,
        text_cleaner="phoneme_cleaners",
        use_phonemes=True,
        phoneme_language="en-us",
        phoneme_cache_path=phoneme_cache_path,
        compute_input_seq_cache=True,
        print_step=5,
        print_eval=True,
        mixed_precision=True if device == "cuda" else False,
        output_path=output_path,
        datasets=[dataset_config],
        cudnn_benchmark=True if device == "cuda" else False,
        test_sentences=[
            ["Kumusta ka"],
            ["Maayong buntag"],
            ["Ang tubig sa suba kay klaro kaayo"],
            ["Gusto ko moadto sa dagat"]
        ]
    )

    # INITIALIZE THE AUDIO PROCESSOR
    ap = AudioProcessor.init_from_config(config)

    # INITIALIZE THE TOKENIZER
    tokenizer, config = TTSTokenizer.init_from_config(config)

    # LOAD DATA SAMPLES
    train_samples, eval_samples = load_tts_samples(
        dataset_config,
        eval_split=True,
        eval_split_max_size=config.eval_split_max_size,
        eval_split_size=config.eval_split_size,
    )

    # init model
    model = Vits(config, ap, tokenizer, speaker_manager=None)
    
    # Move model to appropriate device
    model = model.to(device)

    # Load checkpoint
    print(f"Loading checkpoint from {checkpoint_path}")
    state_dict = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(state_dict['model'])
    
    # Extract step number from checkpoint filename
    step_number = int(os.path.basename(checkpoint_path).split("_")[1].split(".")[0])
    print(f"Resuming from step {step_number}")

    # init the trainer with minimal parameters
    trainer_args = TrainerArgs()  # Use default parameters
    
    # Set the continue_path to the directory, not the file
    trainer_args.continue_path = args.checkpoint_dir
    
    # Run the trainer
    trainer = Trainer(
        trainer_args,
        config,
        output_path,
        model=model,
        train_samples=train_samples,
        eval_samples=eval_samples,
    )
    trainer.fit()

if __name__ == "__main__":
    # Fix for Windows multiprocessing
    import multiprocessing
    multiprocessing.freeze_support()
    main() 