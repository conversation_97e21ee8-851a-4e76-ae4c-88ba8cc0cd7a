import os
import argparse
import torch

from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.vits import Vits, VitsAudioConfig
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor
from trainer import Trainer, TrainerArgs

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train VITS model for Bisaya TTS')
    parser.add_argument('--continue', dest='checkpoint_path', type=str, help='Path to checkpoint to resume training from')
    args = parser.parse_args()

    # Check for available GPU
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Define paths
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "training/bisaya_vits")
    dataset_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data/wavs_augmented")
    phoneme_cache_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "phoneme_cache")

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    os.makedirs(phoneme_cache_path, exist_ok=True)

    # Define dataset config
    dataset_config = BaseDatasetConfig(
        formatter="ljspeech", 
        meta_file_train="metadata_ljspeech.csv", 
        path=dataset_path
    )

    # Define audio config
    audio_config = VitsAudioConfig(
        sample_rate=22050, 
        win_length=1024, 
        hop_length=256, 
        num_mels=80, 
        mel_fmin=0, 
        mel_fmax=None
    )

    # Define VITS configuration
    config = VitsConfig(
        audio=audio_config,
        run_name="bisaya_vits",
        run_description="VITS training for Bisaya language",
        batch_size=16 if device == "cuda" else 4,  # Larger batch size for GPU
        eval_batch_size=8 if device == "cuda" else 2,
        batch_group_size=3,
        num_loader_workers=4 if device == "cuda" else 2,
        num_eval_loader_workers=2 if device == "cuda" else 1,
        run_eval=True,
        test_delay_epochs=5,
        epochs=100,
        text_cleaner="phoneme_cleaners",
        use_phonemes=True,
        phoneme_language="en-us",
        phoneme_cache_path=phoneme_cache_path,
        compute_input_seq_cache=True,
        print_step=5,
        print_eval=True,
        mixed_precision=True if device == "cuda" else False,
        output_path=output_path,
        datasets=[dataset_config],
        cudnn_benchmark=True if device == "cuda" else False,
        test_sentences=[
            ["Kumusta ka"],
            ["Maayong buntag"],
            ["Ang tubig sa suba kay klaro kaayo"],
            ["Gusto ko moadto sa dagat"]
        ]
    )

    # INITIALIZE THE AUDIO PROCESSOR
    ap = AudioProcessor.init_from_config(config)

    # INITIALIZE THE TOKENIZER
    tokenizer, config = TTSTokenizer.init_from_config(config)

    # LOAD DATA SAMPLES
    train_samples, eval_samples = load_tts_samples(
        dataset_config,
        eval_split=True,
        eval_split_max_size=config.eval_split_max_size,
        eval_split_size=config.eval_split_size,
    )

    # init model
    model = Vits(config, ap, tokenizer, speaker_manager=None)
    
    # Move model to appropriate device
    model = model.to(device)

    # Load checkpoint if specified
    if args.checkpoint_path:
        print(f"Loading checkpoint from {args.checkpoint_path}")
        state_dict = torch.load(args.checkpoint_path, map_location=device)
        model.load_state_dict(state_dict['model'])

    # init the trainer with correct parameters
    trainer = Trainer(
        TrainerArgs(
            run_name="bisaya_vits",
            run_description="VITS training for Bisaya language",
            output_path=output_path,
            save_step=1000,           # Save checkpoint every 1000 steps
            save_n_checkpoints=5,     # Keep the last 5 checkpoints
            save_best_after=0,        # Start saving best model immediately
            save_checkpoints=True,    # Enable checkpoint saving
            run_eval=True,           # Enable evaluation
            eval_step=1000,          # Run evaluation every 1000 steps
            print_step=25,           # Print progress every 25 steps
            print_eval=True,         # Print evaluation results
            mixed_precision=True if device == "cuda" else False,     # Enable mixed precision training
            epochs=100,               # Total number of epochs
            text_cleaner="phoneme_cleaners",
            use_phonemes=True,
            phoneme_language="en-us",
            phoneme_cache_path=phoneme_cache_path,
            compute_input_seq_cache=True,
            datasets=[dataset_config],
            cudnn_benchmark=True if device == "cuda" else False,
            test_sentences=[
                ["Kumusta ka"],
                ["Maayong buntag"],
                ["Ang tubig sa suba kay klaro kaayo"],
                ["Gusto ko moadto sa dagat"]
            ]
        ),
        config,
        output_path,
        model=model,
        train_samples=train_samples,
        eval_samples=eval_samples,
    )
    trainer.fit()

if __name__ == "__main__":
    # Fix for Windows multiprocessing
    import multiprocessing
    multiprocessing.freeze_support()
    main() 