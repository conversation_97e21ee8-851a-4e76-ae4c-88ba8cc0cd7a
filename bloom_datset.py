import os
import soundfile as sf
from datasets import load_dataset

# Load Cebuano portion of the Bloom Speech Dataset with trust_remote_code=True
dataset = load_dataset("sil-ai/bloom-speech", "ceb", trust_remote_code=True)

# Base directory to save audio and transcripts
base_dir = "data/bloom_ceb_dataset"
os.makedirs(base_dir, exist_ok=True)

# Function to save audio and transcription
def save_split(split_name, split_data):
    split_dir = os.path.join(base_dir, split_name)
    audio_dir = os.path.join(split_dir, "audio")
    text_file = os.path.join(split_dir, "transcripts.txt")
    os.makedirs(audio_dir, exist_ok=True)

    with open(text_file, "w", encoding="utf-8") as f:
        for i, sample in enumerate(split_data):
            audio_array = sample["audio"]["array"]
            sampling_rate = sample["audio"]["sampling_rate"]
            file_name = f"{split_name}_{i}.wav"
            file_path = os.path.join(audio_dir, file_name)

            # Save audio
            sf.write(file_path, audio_array, sampling_rate)

            # Write transcript
            f.write(f"{file_name}\t{sample['text']}\n")


    print(f"[✓] Saved {split_name} data to {split_dir}")

# Save train, validation, and test splits
for split in dataset:
    save_split(split, dataset[split])
