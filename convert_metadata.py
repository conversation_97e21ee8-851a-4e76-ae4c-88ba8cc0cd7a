import pandas as pd
import os
import shutil

def convert_to_ljspeech_format(input_file, output_file, wav_dir):
    # Read the original metadata
    df = pd.read_csv(input_file)
    
    # Create wavs directory if it doesn't exist
    os.makedirs(wav_dir, exist_ok=True)
    
    # Create a new dataframe in LJSpeech format
    # Format: ID|TEXT|NORMALIZED_TEXT
    # In LJSpeech format, ID is filename without extension, 
    # TEXT is the raw text, 
    # NORMALIZED_TEXT is the same as TEXT for our purpose
    
    data = []
    for _, row in df.iterrows():
        if not pd.isna(row['text']) and row['text'].strip() != '':
            # Get filename without extension
            filename = os.path.splitext(row['file_name'])[0]
            
            # Create LJSpeech format entry
            entry = f"{filename}|{row['text']}|{row['text']}"
            data.append(entry)
            
            # Copy audio file to wavs directory if it exists
            source_file = os.path.join(os.path.dirname(input_file), row['file_name'])
            if os.path.exists(source_file):
                dest_file = os.path.join(wav_dir, row['file_name'])
                shutil.copy2(source_file, dest_file)
                print(f"Copied {row['file_name']} to {wav_dir}")
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in data:
            f.write(entry + '\n')
    
    print(f"Converted {len(data)} entries to LJSpeech format in {output_file}")

if __name__ == "__main__":
    convert_to_ljspeech_format(
        "data/wavs_augmented/metadata.csv", 
        "data/wavs_augmented/metadata_ljspeech.csv",
        "data/wavs_augmented/wavs"
    ) 