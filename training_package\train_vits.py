import os
import torch
import argparse
from TTS.config import load_config
from TTS.trainer import Trainer, TrainingArgs
from TTS.utils.audio import AudioProcessor

def main(config_path, continue_path=None):
    """Train VITS TTS model.
    
    Args:
        config_path (str): Path to config file.
        continue_path (str, optional): Path to checkpoint to continue training from.
    """
    # Load config
    config = load_config(config_path)
    
    # Update paths in config if needed
    if not os.path.exists(config.data.path):
        print(f"Warning: Data path {config.data.path} not found!")
        new_path = input("Please enter the correct data path: ").strip()
        if new_path and os.path.exists(new_path):
            config.data.path = new_path
        else:
            print("Invalid path provided. Exiting.")
            return

    # Setup audio processor
    ap = AudioProcessor(**config.audio)

    # Training arguments
    training_args = TrainingArgs(
        output_path="training/bisaya_vits/",
    )

    # Create trainer instance
    trainer = Trainer(
        training_args,
        config,
        output_path=training_args.output_path,
        audio_processor=ap,
        rank=0,
        gpu=0
    )

    # Load checkpoint if continuing training
    if continue_path and os.path.exists(continue_path):
        print(f"\nLoading checkpoint: {continue_path}")
        try:
            trainer.load_checkpoint(continue_path)
            print("Checkpoint loaded successfully!")
            
            # Get current training step from log file
            log_file = os.path.join(os.path.dirname(continue_path), "trainer_0_log.txt")
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        # Find the last step number
                        for line in reversed(lines):
                            if "STEP:" in line:
                                step = int(line.split("STEP:")[1].split("/")[0].strip())
                                print(f"Continuing from step {step}")
                                break
        except Exception as e:
            print(f"Error loading checkpoint: {str(e)}")
            return
    else:
        print("\nStarting new training session")

    # Start training
    trainer.fit()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--config_path", type=str, default="config.json",
                      help="Path to config file")
    parser.add_argument("--continue_path", type=str, default=None,
                      help="Path to checkpoint file to continue training from")
    
    args = parser.parse_args()
    main(args.config_path, args.continue_path) 