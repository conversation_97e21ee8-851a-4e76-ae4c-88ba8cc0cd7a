#!/usr/bin/env python3
"""
Evaluation script for Bisaya TTS models
Evaluates: 
- Mean Opinion Score (MOS)
- Word Error Rate (WER)
- Spectrogram Comparison
"""

import os
import torch
import torchaudio
import librosa
import librosa.display
import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import ttk
import csv
import datetime
import jiwer
from transformers import Wav2Vec2ForCTC, Wav2Vec2Processor
import argparse

class MOSEvaluator:
    def __init__(self, transfer_learning_dir, no_transfer_learning_dir):
        self.window = tk.Tk()
        self.window.title("Bisaya TTS Quality Evaluation (MOS Test)")
        
        # Load audio samples
        self.samples = {
            'transfer_learning': self._load_audio_samples(transfer_learning_dir),
            'no_transfer_learning': self._load_audio_samples(no_transfer_learning_dir)
        }
        self.scores = []
        self.current_sample = None
        self.setup_gui()
    
    def _load_audio_samples(self, directory):
        """Load audio samples from directory"""
        samples = []
        for file in os.listdir(directory):
            if file.endswith('.wav'):
                samples.append(os.path.join(directory, file))
        return samples
    
    def setup_gui(self):
        # Rating instructions
        instructions = [
            "Rate the speech quality from 1 to 5:",
            "5 - Excellent (Natural, human-like)",
            "4 - Good (Minor issues)",
            "3 - Fair (Noticeable issues)",
            "2 - Poor (Major issues)",
            "1 - Bad (Unintelligible)"
        ]
        
        for instruction in instructions:
            ttk.Label(self.window, text=instruction, padding=5).pack()
        
        # Rating buttons
        self.score_var = tk.StringVar()
        for i in range(1, 6):
            ttk.Radiobutton(self.window, text=str(i), 
                          variable=self.score_var, value=str(i)).pack()
        
        # Control buttons
        ttk.Button(self.window, text="Play Sample", 
                  command=self.play_sample).pack(pady=10)
        ttk.Button(self.window, text="Submit Score", 
                  command=self.submit_score).pack(pady=10)
    
    def save_results(self):
        """Save MOS test results"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f'mos_results_{timestamp}.csv'
        
        with open(results_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['Sample', 'Score', 'Model'])
            writer.writerows(self.scores)
        
        # Calculate average scores
        tl_scores = [float(score) for _, score, model in self.scores 
                    if model == 'transfer_learning']
        ntl_scores = [float(score) for _, score, model in self.scores 
                     if model == 'no_transfer_learning']
        
        print("\nMOS Test Results:")
        print(f"Transfer Learning Model: {sum(tl_scores)/len(tl_scores):.2f}")
        print(f"No Transfer Learning Model: {sum(ntl_scores)/len(ntl_scores):.2f}")
        print(f"Results saved to: {results_file}")

class SpectrogramAnalyzer:
    def __init__(self):
        self.output_dir = "spectrogram_comparisons"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def compare_spectrograms(self, reference_audio, generated_audio, label):
        """Generate and compare spectrograms"""
        # Load audio files
        y_ref, sr_ref = librosa.load(reference_audio)
        y_gen, sr_gen = librosa.load(generated_audio)
        
        # Create spectrograms
        mel_ref = librosa.feature.melspectrogram(y=y_ref, sr=sr_ref)
        mel_gen = librosa.feature.melspectrogram(y=y_gen, sr=sr_gen)
        
        # Convert to dB scale
        mel_ref_db = librosa.power_to_db(mel_ref, ref=np.max)
        mel_gen_db = librosa.power_to_db(mel_gen, ref=np.max)
        
        # Plot comparison
        plt.figure(figsize=(15, 10))
        
        plt.subplot(2, 1, 1)
        librosa.display.specshow(mel_ref_db, sr=sr_ref, x_axis='time', y_axis='mel')
        plt.title('Reference Audio Spectrogram')
        plt.colorbar(format='%+2.0f dB')
        
        plt.subplot(2, 1, 2)
        librosa.display.specshow(mel_gen_db, sr=sr_gen, x_axis='time', y_axis='mel')
        plt.title('Generated Audio Spectrogram')
        plt.colorbar(format='%+2.0f dB')
        
        # Save plot
        output_path = os.path.join(self.output_dir, f'spectrogram_comparison_{label}.png')
        plt.savefig(output_path)
        plt.close()
        
        # Calculate similarity metrics
        mse = np.mean((mel_ref_db - mel_gen_db) ** 2)
        correlation = np.corrcoef(mel_ref_db.flatten(), mel_gen_db.flatten())[0,1]
        
        return {
            'mse': mse,
            'correlation': correlation,
            'plot_path': output_path
        }

class WERCalculator:
    def __init__(self):
        # Initialize Wav2Vec2 model (preferably one fine-tuned for Bisaya)
        self.processor = Wav2Vec2Processor.from_pretrained("facebook/wav2vec2-large-xlsr-53")
        self.model = Wav2Vec2ForCTC.from_pretrained("facebook/wav2vec2-large-xlsr-53")
    
    def calculate_wer(self, audio_path, reference_text):
        """Calculate Word Error Rate"""
        # Load and process audio
        audio_input, _ = torchaudio.load(audio_path)
        inputs = self.processor(audio_input, sampling_rate=16000, return_tensors="pt")
        
        # Get transcription
        with torch.no_grad():
            logits = self.model(inputs.input_values).logits
        predicted_ids = torch.argmax(logits, dim=-1)
        transcription = self.processor.batch_decode(predicted_ids)[0]
        
        # Calculate WER
        wer = jiwer.wer(reference_text, transcription)
        return wer, transcription

def main():
    parser = argparse.ArgumentParser(description='Evaluate Bisaya TTS models')
    parser.add_argument('--tl_dir', type=str, required=True,
                      help='Directory containing transfer learning model outputs')
    parser.add_argument('--ntl_dir', type=str, required=True,
                      help='Directory containing no transfer learning model outputs')
    parser.add_argument('--reference_dir', type=str, required=True,
                      help='Directory containing reference audio files')
    parser.add_argument('--eval_type', type=str, choices=['mos', 'wer', 'spectrogram', 'all'],
                      default='all', help='Type of evaluation to perform')
    args = parser.parse_args()
    
    # Perform evaluations based on args.eval_type
    if args.eval_type in ['mos', 'all']:
        print("\nStarting MOS evaluation...")
        mos_evaluator = MOSEvaluator(args.tl_dir, args.ntl_dir)
        mos_evaluator.window.mainloop()
    
    if args.eval_type in ['spectrogram', 'all']:
        print("\nGenerating spectrogram comparisons...")
        spec_analyzer = SpectrogramAnalyzer()
        # Add your test cases here
        test_cases = [
            {
                'reference': os.path.join(args.reference_dir, 'test1.wav'),
                'tl_output': os.path.join(args.tl_dir, 'test1.wav'),
                'ntl_output': os.path.join(args.ntl_dir, 'test1.wav'),
                'label': 'test1'
            }
            # Add more test cases
        ]
        
        for case in test_cases:
            tl_metrics = spec_analyzer.compare_spectrograms(
                case['reference'], case['tl_output'], f"tl_{case['label']}")
            ntl_metrics = spec_analyzer.compare_spectrograms(
                case['reference'], case['ntl_output'], f"ntl_{case['label']}")
            print(f"\nResults for {case['label']}:")
            print(f"Transfer Learning - MSE: {tl_metrics['mse']:.4f}, Correlation: {tl_metrics['correlation']:.4f}")
            print(f"No Transfer Learning - MSE: {ntl_metrics['mse']:.4f}, Correlation: {ntl_metrics['correlation']:.4f}")
    
    if args.eval_type in ['wer', 'all']:
        print("\nCalculating WER...")
        wer_calculator = WERCalculator()
        # Add your test cases with reference text
        test_cases = [
            {
                'audio': os.path.join(args.tl_dir, 'test1.wav'),
                'reference_text': "Kumusta ka"
            }
            # Add more test cases
        ]
        
        for case in test_cases:
            wer, transcription = wer_calculator.calculate_wer(
                case['audio'], case['reference_text'])
            print(f"\nReference: {case['reference_text']}")
            print(f"Transcribed: {transcription}")
            print(f"WER: {wer:.4f}")

if __name__ == "__main__":
    main()
