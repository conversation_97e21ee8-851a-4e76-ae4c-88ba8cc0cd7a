import os
import torch
from trainer import Trainer

class PatchedTrainer(Trainer):
    """Custom trainer class that fixes the loss comparison issue"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize best_loss to safe values
        self.best_loss = {
            "train_loss": float('inf'),
            "eval_loss": float('inf')
        }
        # Initialize current_loss to safe values
        self.current_loss = {
            "train_loss": float('inf'),
            "eval_loss": float('inf')
        }
        print(f"PatchedTrainer initialized with best_loss: {self.best_loss}")
    
    def save_best_model(self):
        """Patched version of save_best_model that handles None values properly"""
        # Print debug info
        print(f"Current loss: {self.current_loss}")
        print(f"Best loss: {self.best_loss}")
        
        # Ensure both loss dictionaries exist and have proper values
        if not hasattr(self, 'best_loss') or self.best_loss is None:
            self.best_loss = {
                "train_loss": float('inf'),
                "eval_loss": float('inf')
            }
        
        if not hasattr(self, 'current_loss') or self.current_loss is None:
            self.current_loss = {
                "train_loss": float('inf'),
                "eval_loss": float('inf')
            }
        
        # Ensure best_loss has the correct structure
        if not isinstance(self.best_loss, dict):
            self.best_loss = {
                "train_loss": float('inf'),
                "eval_loss": float('inf')
            }
        
        # Ensure current_loss has the correct structure
        if not isinstance(self.current_loss, dict):
            self.current_loss = {
                "train_loss": float('inf'),
                "eval_loss": float('inf')
            }
        
        # Get loss values with safe defaults
        best_train_loss = float(self.best_loss.get("train_loss", float('inf')))
        best_eval_loss = float(self.best_loss.get("eval_loss", float('inf')))
        current_train_loss = float(self.current_loss.get("train_loss", float('inf')))
        current_eval_loss = float(self.current_loss.get("eval_loss", float('inf')))
        
        # Use proper values for comparison
        best_loss = {
            "train_loss": best_train_loss,
            "eval_loss": best_eval_loss
        }
        
        current_loss = {
            "train_loss": current_train_loss,
            "eval_loss": current_eval_loss
        }
        
        # Now check if we should save the model
        use_eval_loss = self.args.use_eval_loss
        if (use_eval_loss and current_loss["eval_loss"] < best_loss["eval_loss"]) or (
            not use_eval_loss and current_loss["train_loss"] < best_loss["train_loss"]
        ):
            print(f"Saving best model with loss: {current_loss}")
            self.best_loss = current_loss.copy()
            
            # Create output directory if it doesn't exist
            os.makedirs(self.output_path, exist_ok=True)
            
            # Build checkpoint dictionary
            checkpoint = {
                "model": self.model.state_dict(),
                "optimizer": self.optimizer.state_dict(),
                "best_loss": self.best_loss,
                "step": self.step,
                "epoch": self.epoch,
                "scaler": None if self.scaler is None else self.scaler.state_dict(),
            }
            
            # Save checkpoint
            checkpoint_path = os.path.join(self.output_path, "best_model.pth")
            torch.save(checkpoint, checkpoint_path)
            
            print(f" > Best model saved to {checkpoint_path}")
            return True
        return False 