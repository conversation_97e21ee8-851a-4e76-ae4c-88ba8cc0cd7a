import os
import torch
import argparse
from TTS.utils.synthesizer import Synthesizer

def main():
    parser = argparse.ArgumentParser(description='Generate speech using VITS model')
    parser.add_argument('--text', type=str, required=True, help='Text to synthesize')
    parser.add_argument('--model_dir', type=str, default="training/bisaya_vits/bisaya_vits-April-10-2025_01+57AM-10597ee",
                      help='Directory containing the checkpoints')
    parser.add_argument('--checkpoint', type=str, default="checkpoint_30000.pth",
                      help='Specific checkpoint file to use')
    parser.add_argument('--output_dir', type=str, default="generated_audio",
                      help='Directory to save generated audio')
    args = parser.parse_args()

    # Check if model directory exists
    if not os.path.exists(args.model_dir):
        print(f"Error: Model directory '{args.model_dir}' does not exist.")
        return

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Define paths
    model_path = os.path.join(args.model_dir, args.checkpoint)
    config_path = os.path.join(args.model_dir, "config.json")
    
    if not os.path.exists(model_path):
        print(f"Error: Checkpoint file '{model_path}' does not exist.")
        return
        
    if not os.path.exists(config_path):
        print(f"Error: Config file '{config_path}' does not exist.")
        return

    print(f"\nUsing checkpoint: {args.checkpoint}")
    
    # Create synthesizer
    synthesizer = Synthesizer(
        model_path,
        config_path,
        use_cuda=torch.cuda.is_available()
    )

    # Generate speech
    print(f"\nGenerating speech for text: '{args.text}'")
    outputs = synthesizer.tts(args.text)
    
    # Save the generated audio
    output_file = os.path.join(args.output_dir, f"generated_speech_{args.checkpoint[:-4]}.wav")
    synthesizer.save_wav(outputs, output_file)
    print(f"\nSpeech generated and saved to: {output_file}")

if __name__ == "__main__":
    main() 