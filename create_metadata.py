import os
import pandas as pd

def create_metadata():
    # Directory containing the WAV files
    wav_dir = "data/wavs_resampled"
    
    # Lists to store data
    file_names = []
    texts = []
    
    # Process each WAV file
    for filename in os.listdir(wav_dir):
        if filename.endswith(".wav"):
            # Get the text from filename (remove .wav and replace underscores with spaces)
            text = filename[:-4].replace("_", " ")
            
            # Add to lists
            file_names.append(filename)
            texts.append(text)
    
    # Create DataFrame
    df = pd.DataFrame({
        "file_name": file_names,
        "text": texts,
        "speaker_name": ["bisaya_speaker"] * len(file_names)  # Using a single speaker for now
    })
    
    # Save to CSV
    df.to_csv("metadata.csv", index=False)
    print(f"Created metadata.csv with {len(df)} entries")

if __name__ == "__main__":
    create_metadata() 