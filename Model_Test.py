import os
import torch
from TTS.tts.models.vits import Vits  # Adjust this import based on your model's structure
import soundfile as sf

# Load the model
model_path = 'training/bisaya_vits/bisaya_vits_bloom-April-15-2025_06+06AM-0000000/best_model_1026.pth'
model = Vits.load(model_path)  # Adjust this line based on your model loading method
model.eval()  # Set the model to evaluation mode

# Prepare input text
input_text = "maayong udto, mangaon ta kaninyo"

# Generate audio
audio_output = model.generate(input_text)  # Adjust this line based on your model's API

# Save the output as a .wav file
output_wav_path = 'generated_audio.wav'  # Specify the output path
sf.write(output_wav_path, audio_output, 22050)  # Adjust sample rate as necessary
print(f"Generated audio saved to: {output_wav_path}")