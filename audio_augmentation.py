import os
import librosa
import soundfile as sf
import numpy as np
from pydub import AudioSegment
from pathlib import Path

def augment_audio(input_dir, output_dir):
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    pitch_shifts = [3, -3]
    time_stretches = [0.8, 1.2]
    speed_changes = [0.8, 1.2]
    
    for file_path in Path(input_dir).glob('*.wav'):
        # Load audio with explicit float32 format
        audio, sr = librosa.load(file_path, sr=None, dtype=np.float32)
        base_name = file_path.stem
        ext = file_path.suffix
        
        # Save original file
        sf.write(Path(output_dir) / file_path.name, audio, sr)
        
        # 1. Pitch Shifting with resampling workaround
        for n_steps in pitch_shifts:
            try:
                pitched_audio = librosa.effects.pitch_shift(
                    audio, 
                    sr=sr, 
                    n_steps=n_steps,
                    res_type='soxr_hq'  # Explicit resampling type
                )
                new_name = f"{base_name}_pitch_{n_steps}{ext}"
                sf.write(Path(output_dir) / new_name, pitched_audio, sr)
            except Exception as e:
                print(f"Error processing pitch shift for {file_path.name}: {str(e)}")
        
        # 2. Time Stretching with dtype enforcement
        for rate in time_stretches:
            try:
                stretched_audio = librosa.effects.time_stretch(
                    audio.astype(np.float64),  # Force float64 for compatibility
                    rate=rate
                )
                new_name = f"{base_name}_time_{rate}{ext}"
                sf.write(Path(output_dir) / new_name, stretched_audio.astype(np.float32), sr)
            except Exception as e:
                print(f"Error processing time stretch for {file_path.name}: {str(e)}")
        
        # 3. Speed Change using pydub
        for speed in speed_changes:
            try:
                audio_segment = AudioSegment.from_wav(file_path)
                speed_audio = audio_segment.speedup(
                    playback_speed=speed,
                    chunk_size=150,
                    crossfade=25
                )
                new_name = f"{base_name}_speed_{speed}{ext}"
                speed_audio.export(Path(output_dir) / new_name, format="wav")
            except Exception as e:
                print(f"Error processing speed change for {file_path.name}: {str(e)}")

if __name__ == "__main__":
    input_directory = "data/wavs_resampled"
    output_directory = "data/wavs_augmented"
    
    augment_audio(input_directory, output_directory)
    print(f"Augmentation complete. Check {output_directory} for results.")