import os
import torch
import sys
import time
import numpy as np
from TTS.utils.manage import ModelManager
from TTS.utils.synthesizer import Synthesizer
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.models.vits import Vits
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor

# Set the paths
MODEL_PATH = "colab_project/training/bisaya_vits_bloom/bisaya_vits_bloom-April-28-2025_06+05AM-0000000-20250508T023814Z-001/bisaya_vits_bloom-April-28-2025_06+05AM-0000000/best_model_6622.pth"
CONFIG_PATH = "colab_project/training/bisaya_vits_bloom/bisaya_vits_bloom-April-28-2025_06+05AM-0000000-20250508T023814Z-001/bisaya_vits_bloom-April-28-2025_06+05AM-0000000/config.json"
OUT_PATH = "test_outputs/test13_collab.wav"

# Get user input if provided, otherwise use default
TEXT = sys.argv[1] if len(sys.argv) > 1 else "Unsa imong pangalan"

print(f"Loading model from {MODEL_PATH}")
print(f"Text to synthesize: {TEXT}")

# Load the config
config = VitsConfig()
config.load_json(CONFIG_PATH)

# IMPORTANT: Fix the character count to match the pre-trained model
config.model_args["num_chars"] = 177
print(f"Adjusted config.model_args['num_chars'] to 177 to match pre-trained model")

# Initialize audio processor and tokenizer from config
ap = AudioProcessor.init_from_config(config)
tokenizer, config = TTSTokenizer.init_from_config(config)

# Create the model
model = Vits(config, ap, tokenizer, speaker_manager=None)

# Custom checkpoint loading handler
def load_checkpoint(checkpoint_path, model):
    print(f"Loading checkpoint from {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Print model parameter shapes before loading
    print("\nCurrent model embedding shape:", model.text_encoder.emb.weight.shape)
    
    # Print checkpoint parameter shapes
    checkpoint_emb_shape = checkpoint['model']['text_encoder.emb.weight'].shape
    print("Checkpoint embedding shape:", checkpoint_emb_shape)
    
    # Handle the mismatch by recreating the embedding layer with correct dimensions
    if model.text_encoder.emb.weight.shape != checkpoint_emb_shape:
        print(f"Recreating embedding layer to match checkpoint dimensions: {checkpoint_emb_shape}")
        hidden_channels = checkpoint_emb_shape[1]
        model.text_encoder.emb = torch.nn.Embedding(checkpoint_emb_shape[0], hidden_channels)
        print("New model embedding shape:", model.text_encoder.emb.weight.shape)
    
    # Load the state dictionary now that dimensions match
    try:
        model.load_state_dict(checkpoint['model'])
        print("Checkpoint loaded successfully!")
    except Exception as e:
        print(f"Warning: Error loading checkpoint: {e}")
        print("Trying again with strict=False...")
        model.load_state_dict(checkpoint['model'], strict=False)
        print("Checkpoint loaded with strict=False")
    
    return model

# Custom text preprocessing function
def preprocess_text(text, tokenizer, max_token_id=176):
    """Process text with the tokenizer but constrain token IDs to the max value"""
    print(f"Original text: '{text}'")
    
    # Get the original token IDs
    if tokenizer.use_phonemes:
        token_ids = tokenizer.text_to_ids(text)
    else:
        token_ids = tokenizer.text_to_ids(text, False)
    
    print(f"Original token IDs: {token_ids}")
    
    # Constrain token IDs to be within the valid range
    constrained_ids = [min(id, max_token_id) for id in token_ids]
    
    if token_ids != constrained_ids:
        print(f"Some tokens were out of range and have been constrained")
        print(f"Constrained token IDs: {constrained_ids}")
    
    return constrained_ids

# Load the model
model = load_checkpoint(MODEL_PATH, model)
model.eval()

if torch.cuda.is_available():
    model = model.cuda()
    print("Model moved to GPU")
else:
    print("Using CPU for inference (slower)")

# Synthesize speech
print(f"\nSynthesizing: '{TEXT}'")
with torch.no_grad():
    start_time = time.time()
    
    # Process text with the custom function
    max_token_id = model.text_encoder.emb.weight.shape[0] - 1
    print(f"Max token ID allowed: {max_token_id}")
    
    text_inputs = preprocess_text(TEXT, tokenizer, max_token_id)
    
    # Add batch dimension
    text_inputs = torch.LongTensor(text_inputs).unsqueeze(0)
    x_lengths = torch.LongTensor([text_inputs.shape[1]])
    
    if torch.cuda.is_available():
        text_inputs = text_inputs.cuda()
        x_lengths = x_lengths.cuda()
    
    # Fix for aux_input - Create a proper empty aux_input dictionary
    aux_input = {"speaker_ids": None, "d_vectors": None, "language_ids": None}
    
    # Calculate text embedding and other features directly
    print("Running model components manually...")
    try:
        # Get text encoder outputs
        x, m_p, logs_p, x_mask = model.text_encoder(text_inputs, x_lengths)
        
        # Flow-based decoder
        z_p = model.flow(x, x_mask, False)
        
        # Waveform decoder
        o = model.waveform_decoder((z_p * x_mask) * model.model_args.inference_noise_scale, g=None)
        waveform = o.squeeze().cpu().numpy()
        
        end_time = time.time()
        print(f"Synthesis completed in {end_time - start_time:.2f} seconds")
        
        # Save the audio file
        ap.save_wav(waveform, OUT_PATH, config.audio.sample_rate)
        print(f"Audio saved to {OUT_PATH}")
    except Exception as e:
        print(f"Error during manual synthesis: {e}")
        print("\nTrying model.inference() directly...")
        
        # Try using the model.inference method as a fallback
        outputs = model.inference(text_inputs, aux_input)
        waveform = outputs["waveform"].squeeze().cpu().numpy()
        
        ap.save_wav(waveform, OUT_PATH, config.audio.sample_rate)
        print(f"Audio saved to {OUT_PATH}") 