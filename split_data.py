import os
import csv
import shutil
from pathlib import Path
from sklearn.model_selection import train_test_split

# Configuration
audio_dir = "data/wavs_augmented"  # Directory containing the audio files
output_dir = "data/split_data"  # Directory to save the split data
metadata_file = "data/metadata_final.csv"  # Metadata file to create
train_ratio = 0.8  # 80% for training
val_ratio = 0.1  # 10% for validation
test_ratio = 0.1  # 10% for testing

# Create output directories
train_dir = Path(output_dir) / "train"
val_dir = Path(output_dir) / "val"
test_dir = Path(output_dir) / "test"

train_dir.mkdir(parents=True, exist_ok=True)
val_dir.mkdir(parents=True, exist_ok=True)
test_dir.mkdir(parents=True, exist_ok=True)

# Get list of all audio files
audio_files = list(Path(audio_dir).glob("*.wav"))
print(f"Total audio files: {len(audio_files)}")

# Split data into train, validation, and test sets
train_files, temp_files = train_test_split(audio_files, test_size=(1 - train_ratio), random_state=42)
val_files, test_files = train_test_split(temp_files, test_size=test_ratio / (val_ratio + test_ratio), random_state=42)

print(f"Train files: {len(train_files)}")
print(f"Validation files: {len(val_files)}")
print(f"Test files: {len(test_files)}")

# Function to copy files to their respective directories
def copy_files(files, destination):
    for file in files:
        shutil.copy(file, destination / file.name)

# Copy files to their respective directories
copy_files(train_files, train_dir)
copy_files(val_files, val_dir)
copy_files(test_files, test_dir)

# Create metadata file
def create_metadata(files, split_name):
    metadata = []
    for file in files:
        file_path = str(Path(split_name) / file.name)
        base_name = file.stem
        parts = base_name.split("_")
        transcription = " ".join(parts[1:-2])  # Extract transcription text
        speaker_id = parts[-1]  # Extract speaker ID
        metadata.append([file_path, transcription, speaker_id])
    return metadata

# Generate metadata for train, validation, and test sets
train_metadata = create_metadata(train_files, "train")
val_metadata = create_metadata(val_files, "val")
test_metadata = create_metadata(test_files, "test")

# Combine all metadata
all_metadata = train_metadata + val_metadata + test_metadata

# Write metadata to CSV
with open(metadata_file, "w", newline="", encoding="utf-8") as f:
    writer = csv.writer(f, delimiter="|")
    writer.writerow(["file_path", "transcription", "speaker_id"])  # Write header
    writer.writerows(all_metadata)  # Write rows

print(f"Metadata file created: {metadata_file}")