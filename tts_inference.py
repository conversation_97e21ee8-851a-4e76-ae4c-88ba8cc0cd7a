import os
import torch
import argparse
import time
import numpy as np
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.models.vits import Vits
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor
from TTS.utils.synthesizer import Synthesizer

def fix_checkpoint_embedding(checkpoint_path, output_path=None):
    """Fixes the checkpoint embedding size to match the config."""
    if output_path is None:
        # If no output path is specified, create one based on the input
        dirname = os.path.dirname(checkpoint_path)
        basename = os.path.basename(checkpoint_path)
        output_path = os.path.join(dirname, f"fixed_{basename}")
    
    print(f"Loading checkpoint: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Get the embedding weight shape
    emb_weight = checkpoint['model']['text_encoder.emb.weight']
    original_shape = emb_weight.shape
    print(f"Original embedding shape: {original_shape}")
    
    # Create a new embedding with the target shape (179 x 192)
    target_shape = (179, original_shape[1])  # Keep the same embedding dimension
    
    if original_shape == target_shape:
        print("Embedding shape already matches the target. No need to fix.")
        return checkpoint_path
    
    # Create a new weight tensor with the target shape
    new_emb_weight = torch.zeros(target_shape)
    
    # Copy the original embeddings to the new tensor
    # (preserve as many of the original weights as possible)
    min_rows = min(original_shape[0], target_shape[0])
    new_emb_weight[:min_rows, :] = emb_weight[:min_rows, :]
    
    # If the new embedding is larger, initialize the new rows
    # (typically with the mean or small random values)
    if target_shape[0] > original_shape[0]:
        # Initialize with the mean of existing embeddings
        mean_emb = emb_weight.mean(dim=0)
        for i in range(original_shape[0], target_shape[0]):
            new_emb_weight[i] = mean_emb + torch.randn_like(mean_emb) * 0.01  # Small random variation
    
    # Update the checkpoint with the new embedding
    checkpoint['model']['text_encoder.emb.weight'] = new_emb_weight
    
    print(f"New embedding shape: {new_emb_weight.shape}")
    print(f"Saving fixed checkpoint to: {output_path}")
    
    # Save the modified checkpoint
    torch.save(checkpoint, output_path)
    print("Checkpoint fixed successfully!")
    
    return output_path

def synthesize_text(text, model_path, config_path, output_path):
    """
    Synthesize speech using a VITS model with appropriate error handling.
    """
    print(f"Text to synthesize: '{text}'")
    print(f"Model path: {model_path}")
    print(f"Config path: {config_path}")
    print(f"Output path: {output_path}")
    
    # First fix the checkpoint if needed
    fixed_model_path = fix_checkpoint_embedding(model_path)
    
    try:
        print("Attempting synthesis using TTS Synthesizer...")
        
        # Initialize a synthesizer with the fixed checkpoint
        synthesizer = Synthesizer(
            tts_checkpoint=fixed_model_path,
            tts_config_path=config_path,
            vocoder_checkpoint=None,
            vocoder_config=None,
            use_cuda=torch.cuda.is_available()
        )
        
        # Synthesize with the standard interface
        outputs = synthesizer.tts(text)
        synthesizer.save_wav(outputs, output_path)
        
        print(f"Synthesis successful! Audio saved to {output_path}")
        return
    
    except Exception as e:
        print(f"Error with standard synthesizer: {e}")
        print("\nFalling back to manual synthesis...")
    
    # Fallback to manual synthesis
    try:
        # Load the config
        config = VitsConfig()
        config.load_json(config_path)
        
        # Make sure the character count matches the checkpoint
        checkpoint = torch.load(fixed_model_path, map_location='cpu')
        emb_shape = checkpoint['model']['text_encoder.emb.weight'].shape
        config.model_args["num_chars"] = emb_shape[0]
        print(f"Adjusted model_args.num_chars to {emb_shape[0]}")
        
        # Initialize audio processor and tokenizer
        ap = AudioProcessor.init_from_config(config)
        tokenizer, config = TTSTokenizer.init_from_config(config)
        
        # Initialize the model
        model = Vits(config, ap, tokenizer, speaker_manager=None)
        
        # Load model weights with strict=False
        model.load_state_dict(checkpoint['model'], strict=False)
        print("Model loaded successfully with strict=False")
        
        model.eval()
        
        if torch.cuda.is_available():
            model = model.cuda()
            print("Using GPU for inference")
        else:
            print("Using CPU for inference (slower)")
        
        # Process text to get token IDs
        with torch.no_grad():
            # Tokenize the text safely
            max_token_id = model.text_encoder.emb.weight.shape[0] - 1
            print(f"Max token ID allowed: {max_token_id}")
            
            if config.use_phonemes:
                token_ids = tokenizer.text_to_ids(text)
            else:
                token_ids = tokenizer.text_to_ids(text, False)
            
            # Ensure token IDs are within range
            token_ids = [min(id, max_token_id) for id in token_ids]
            
            # Process the text to get waveform
            x = torch.LongTensor(token_ids).unsqueeze(0)
            x_lengths = torch.LongTensor([len(token_ids)])
            
            if torch.cuda.is_available():
                x = x.cuda()
                x_lengths = x_lengths.cuda()
            
            # Create empty speaker/language info for inference
            aux_input = {"speaker_ids": None, "d_vectors": None, "language_ids": None}
            
            # Run inference manually
            print("Running inference...")
            start_time = time.time()
            
            # Get text encoder outputs
            text_outputs = model.text_encoder(x, x_lengths)
            x = text_outputs[0]  # encoder outputs
            x_mask = text_outputs[3]  # encoder output mask
            
            # Process through flow
            z_p = None
            try:
                # Try direct synthesis through model.inference with fixed aux_input
                outputs = model.inference(x, aux_input)
                waveform = outputs["waveform"].squeeze().numpy()
            except Exception as e:
                print(f"Direct inference failed: {e}")
                print("Trying alternative synthesis method...")
                
                # Generate noise with appropriate shape
                z_p = torch.randn(x.shape[0], 192, x.shape[2]) * 0.667
                if torch.cuda.is_available():
                    z_p = z_p.cuda()
                z_p = z_p * x_mask
                
                # Decode directly
                o = model.waveform_decoder(z_p, None)
                waveform = o.squeeze().cpu().numpy()
            
            end_time = time.time()
            print(f"Synthesis completed in {end_time - start_time:.2f} seconds")
            
            # Save the audio file
            ap.save_wav(waveform, output_path, config.audio.sample_rate)
            print(f"Audio saved to {output_path}")
            return
    
    except Exception as e:
        print(f"Manual synthesis also failed: {e}")
        print("Could not synthesize audio. Please check the model compatibility.")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Synthesize speech with VITS model")
    parser.add_argument("--text", type=str, required=True, help="Text to synthesize")
    parser.add_argument("--model_path", type=str, required=True, help="Path to the model checkpoint")
    parser.add_argument("--config_path", type=str, required=True, help="Path to the model configuration")
    parser.add_argument("--output_path", type=str, required=True, help="Path to save the output audio")
    
    args = parser.parse_args()
    synthesize_text(args.text, args.model_path, args.config_path, args.output_path) 