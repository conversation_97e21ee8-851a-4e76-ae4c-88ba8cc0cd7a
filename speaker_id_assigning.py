import os
import csv

# Define the input folder and output metadata file
input_folder = "wavs_3"  # Change this to match your folder path
output_csv = "data/metadata_speaker_3.csv"

# Get list of all .wav files in the folder
wav_files = [f for f in os.listdir(input_folder) if f.endswith(".wav")]

# Sort files to ensure consistent ordering
wav_files.sort()

# Define a default speaker number (modify as needed)
default_speaker_no = 3  # You can manually edit this in the CSV later

# Create metadata.csv
with open(output_csv, mode="w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(["ID Number", "Transcription Text", "Bisaya Speaker No."])  # Header row

    for filename in wav_files:
        # Extract ID and transcription from filename
        parts = filename.split("_", 1)  # Splitting on first underscore
        if len(parts) == 2:
            id_number = parts[0]  # First part before underscore
            transcription = parts[1].replace(".wav", "").replace("_", " ")  # Clean transcription
        else:
            id_number = "000"  # Fallback if no underscore found
            transcription = filename.replace(".wav", "").replace("_", " ")

        # Write row to CSV
        writer.writerow([id_number, transcription, default_speaker_no])

print(f"Metadata CSV created: {output_csv}")
