# Import necessary libraries
from TTS.utils.manage import ModelManager
import os
import glob
import shutil

# Create model manager
manager = ModelManager()

# Download the LJSpeech VITS model
print("Downloading LJSpeech VITS model...")
model_name = "tts_models/en/ljspeech/vits"
download_result = manager.download_model(model_name)

# Extract the model path from the result tuple
# The first element of the tuple is the model file path
model_file_path = download_result[0]
# The second element is the config JSON file path
config_file_path = download_result[1]
print(f"Model file downloaded to: {model_file_path}")

# Create a directory to store the pre-trained model and config
project_dir = "colab_project"
pretrained_models_dir = os.path.join(project_dir, "pretrained_models")
os.makedirs(pretrained_models_dir, exist_ok=True)

# Copy the model file
destination_model_path = os.path.join(pretrained_models_dir, "ljspeech_vits_pretrained.pth")
shutil.copy2(model_file_path, destination_model_path)
print(f"Model file copied to: {destination_model_path}")

# Copy the config JSON file
destination_config_path = os.path.join(pretrained_models_dir, "vits_config.json")
shutil.copy2(config_file_path, destination_config_path)
print(f"Config file copied to: {destination_config_path}")

# Print the command to use for training
print("\n" + "="*50)
print("To use this pre-trained model for transfer learning, run:")
print(f"python train_vits.py --pretrained_model_path {destination_model_path}")
print("="*50)

# Print information about the model
print("\nModel Information:")
print(f"Model type: VITS (Variational Inference with adversarial learning for end-to-end Text-to-Speech)")
print(f"Training dataset: LJSpeech (English single female speaker)")
print(f"Features: End-to-end TTS model capable of high-quality speech synthesis")
print(f"Original language: English")
print(f"Target language: Bisaya (for transfer learning)")