import os

def verify_upload():
    # Check main directories
    print("Checking directory structure...")
    required_dirs = [
        "data/bloom_ceb_dataset",
        "data/bloom_ceb_dataset/train",
        "data/bloom_ceb_dataset/train/audio",
        "data/bloom_ceb_dataset/validation",
        "data/bloom_ceb_dataset/validation/audio",
        "data/bloom_ceb_dataset/test",
        "data/bloom_ceb_dataset/test/audio",
        "formatters"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ {dir_path} exists")
        else:
            print(f"✗ {dir_path} missing!")
    
    # Check critical files
    print("\nChecking critical files...")
    required_files = [
        "data/bloom_ceb_dataset/train/transcripts.txt",
        "data/bloom_ceb_dataset/validation/transcripts.txt",
        "data/bloom_ceb_dataset/test/transcripts.txt",
        "formatters/bloom.py",
        "train_vits.py",
        "requirements.txt"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing!")

if __name__ == "__main__":
    verify_upload() 