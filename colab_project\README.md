# Bisaya TTS - Google Colab Project

This directory contains files for training a VITS TTS model for the Bisaya language using Google Colab.

## Directory Structure

```
colab_project/
├── Bisaya_TTS_Training.ipynb  # Colab notebook template
├── README.md                  # This file
├── requirements.txt           # Python dependencies
├── train_vits.py              # Training script
└── data/                      # Dataset directory
    └── bloom_ceb_dataset/     # Bloom Cebuano dataset
        └── train/             
            ├── audio/         # Original audio files 
            ├── transcripts.txt # Tab-separated transcription file
```

## How to Use

1. **Upload to Google Drive**:
   - Upload this entire directory to your Google Drive.

2. **Open the Notebook in Colab**:
   - Navigate to the `Bisaya_TTS_Training.ipynb` file in Google Drive
   - Right-click and select "Open with Google Colaboratory"

3. **Upload Your Dataset**:
   - You have several options to get your dataset into Colab:
     - Upload the dataset to Google Drive and copy it to Colab
     - Upload directly to <PERSON><PERSON> using the file upload feature
     - Download from a URL if your dataset is hosted somewhere

4. **Follow the Notebook Steps**:
   - The notebook contains detailed steps for:
     - Setting up the environment
     - Preparing the data
     - Training the model
     - Monitoring training progress
     - Saving and testing the trained model

5. **Save Your Trained Model**:
   - Your trained model will be saved in the `training` directory
   - You can download it or save it to Google Drive

## Dataset Format

The dataset should follow this format:

1. Audio files should be in WAV format placed in the `data/bloom_ceb_dataset/train/audio/` directory
2. A transcription file (`transcripts.txt`) should be in the format:
   ```
   filename.wav	transcription text
   ```
   Where each line contains a filename and its transcription separated by a tab character

## GPU Requirements

VITS training requires a GPU. The notebook will use whatever GPU is allocated by Google Colab.

## Additional Notes

- Training a full VITS model can take many hours or days even on a GPU
- Google Colab sessions have time limits (12-24 hours), so you may need to resume training multiple times
- You can adjust the training parameters in the script for your specific needs 