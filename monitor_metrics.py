"""
Bisaya TTS Training Metrics Monitor

This script monitors and documents performance metrics for Bisaya TTS training.
It extracts metrics from training logs and generates visualizations and reports
for research documentation.
"""

import os
import re
import json
import csv
import matplotlib.pyplot as plt
import numpy as np
import datetime
from pathlib import Path

def parse_log_file(log_file):
    """Parse the training log file and extract metrics."""
    metrics = {
        'steps': [],
        'timestamps': [],
        'total_loss': [],
        'gen_loss': [],
        'disc_loss': [],
        'duration_loss': [],
        'mel_loss': [],
        'kl_loss': [],
        'epochs': [],
    }
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            log_content = f.readlines()
        
        current_epoch = 0
        
        for line in log_content:
            # Check for epoch information
            epoch_match = re.search(r'Epoch: (\d+)', line)
            if epoch_match:
                current_epoch = int(epoch_match.group(1))
            
            # Extract step information
            step_match = re.search(r'STEP: (\d+)', line)
            if step_match:
                step = int(step_match.group(1))
                metrics['steps'].append(step)
                metrics['epochs'].append(current_epoch)
                
                # Extract timestamp
                timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if timestamp_match:
                    metrics['timestamps'].append(timestamp_match.group(1))
                else:
                    metrics['timestamps'].append(None)
                
                # Extract loss values
                total_loss_match = re.search(r'LOSS: ([\d\.]+)', line)
                if total_loss_match:
                    metrics['total_loss'].append(float(total_loss_match.group(1)))
                else:
                    metrics['total_loss'].append(None)
                
                gen_loss_match = re.search(r'GEN_LOSS: ([\d\.]+)', line)
                if gen_loss_match:
                    metrics['gen_loss'].append(float(gen_loss_match.group(1)))
                else:
                    metrics['gen_loss'].append(None)
                
                disc_loss_match = re.search(r'DISC_LOSS: ([\d\.]+)', line)
                if disc_loss_match:
                    metrics['disc_loss'].append(float(disc_loss_match.group(1)))
                else:
                    metrics['disc_loss'].append(None)
                
                duration_loss_match = re.search(r'DUR_LOSS: ([\d\.]+)', line)
                if duration_loss_match:
                    metrics['duration_loss'].append(float(duration_loss_match.group(1)))
                else:
                    metrics['duration_loss'].append(None)
                
                mel_loss_match = re.search(r'MEL_LOSS: ([\d\.]+)', line)
                if mel_loss_match:
                    metrics['mel_loss'].append(float(mel_loss_match.group(1)))
                else:
                    metrics['mel_loss'].append(None)
                
                kl_loss_match = re.search(r'KL_LOSS: ([\d\.]+)', line)
                if kl_loss_match:
                    metrics['kl_loss'].append(float(kl_loss_match.group(1)))
                else:
                    metrics['kl_loss'].append(None)
        
        return metrics
    except Exception as e:
        print(f"Error parsing log file: {e}")
        return None

def plot_metrics(metrics, output_dir):
    """Plot the metrics and save to the output directory."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Create timestamp for the report
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    
    # Filter out None values
    steps = metrics['steps']
    epochs = metrics['epochs']
    
    # Total loss plot
    plt.figure(figsize=(12, 6))
    total_loss = [x for x in metrics['total_loss'] if x is not None]
    if total_loss:
        valid_steps = steps[:len(total_loss)]
        plt.plot(valid_steps, total_loss)
        plt.title('Total Loss over Training Steps')
        plt.xlabel('Step')
        plt.ylabel('Loss')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, f'total_loss_{timestamp}.png'))
        plt.close()
    
    # Generator vs Discriminator loss
    plt.figure(figsize=(12, 6))
    gen_loss = [x for x in metrics['gen_loss'] if x is not None]
    disc_loss = [x for x in metrics['disc_loss'] if x is not None]
    if gen_loss and disc_loss:
        min_len = min(len(gen_loss), len(disc_loss))
        valid_steps = steps[:min_len]
        plt.plot(valid_steps, gen_loss[:min_len], label='Generator Loss')
        plt.plot(valid_steps, disc_loss[:min_len], label='Discriminator Loss')
        plt.title('Generator vs Discriminator Loss')
        plt.xlabel('Step')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, f'gen_disc_loss_{timestamp}.png'))
        plt.close()
    
    # Component losses
    plt.figure(figsize=(12, 6))
    components = ['duration_loss', 'mel_loss', 'kl_loss']
    for component in components:
        values = [x for x in metrics[component] if x is not None]
        if values:
            valid_steps = steps[:len(values)]
            plt.plot(valid_steps, values, label=component.replace('_', ' ').title())
    
    plt.title('Component Losses')
    plt.xlabel('Step')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, f'component_losses_{timestamp}.png'))
    plt.close()
    
    # Average loss by epoch
    epoch_losses = {}
    for i, epoch in enumerate(epochs):
        if metrics['total_loss'][i] is not None:
            if epoch not in epoch_losses:
                epoch_losses[epoch] = []
            epoch_losses[epoch].append(metrics['total_loss'][i])
    
    avg_losses = []
    epoch_nums = []
    for epoch, losses in epoch_losses.items():
        avg_losses.append(sum(losses) / len(losses))
        epoch_nums.append(epoch)
    
    plt.figure(figsize=(12, 6))
    plt.plot(epoch_nums, avg_losses)
    plt.title('Average Loss by Epoch')
    plt.xlabel('Epoch')
    plt.ylabel('Average Loss')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, f'epoch_avg_loss_{timestamp}.png'))
    plt.close()
    
    # Export metrics to CSV
    csv_file = os.path.join(output_dir, f'metrics_{timestamp}.csv')
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        # Write header
        header = ['Step', 'Epoch', 'Timestamp', 'Total Loss', 'Generator Loss', 
                  'Discriminator Loss', 'Duration Loss', 'Mel Loss', 'KL Loss']
        writer.writerow(header)
        
        # Write data
        for i in range(len(steps)):
            row = [
                steps[i],
                epochs[i],
                metrics['timestamps'][i],
                metrics['total_loss'][i],
                metrics['gen_loss'][i],
                metrics['disc_loss'][i],
                metrics['duration_loss'][i],
                metrics['mel_loss'][i],
                metrics['kl_loss'][i]
            ]
            writer.writerow(row)
    
    # Generate summary report
    summary_file = os.path.join(output_dir, f'summary_{timestamp}.txt')
    with open(summary_file, 'w') as f:
        f.write("Bisaya TTS Training Metrics Summary\n")
        f.write("===================================\n\n")
        f.write(f"Report generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("Training Statistics:\n")
        f.write(f"Total steps: {len(steps)}\n")
        f.write(f"Total epochs: {max(epochs) if epochs else 0}\n")
        
        if metrics['total_loss']:
            valid_loss = [x for x in metrics['total_loss'] if x is not None]
            if valid_loss:
                f.write(f"Initial loss: {valid_loss[0]:.4f}\n")
                f.write(f"Final loss: {valid_loss[-1]:.4f}\n")
                f.write(f"Loss reduction: {valid_loss[0] - valid_loss[-1]:.4f} ({(1 - valid_loss[-1]/valid_loss[0])*100:.2f}%)\n")
        
        f.write("\nEpoch-wise Average Losses:\n")
        for epoch, avg_loss in zip(epoch_nums, avg_losses):
            f.write(f"Epoch {epoch}: {avg_loss:.4f}\n")
    
    print(f"Metrics plotted and saved to {output_dir}")
    
    return {
        'plots': [
            os.path.join(output_dir, f'total_loss_{timestamp}.png'),
            os.path.join(output_dir, f'gen_disc_loss_{timestamp}.png'),
            os.path.join(output_dir, f'component_losses_{timestamp}.png'),
            os.path.join(output_dir, f'epoch_avg_loss_{timestamp}.png')
        ],
        'csv': csv_file,
        'summary': summary_file
    }

if __name__ == "__main__":
    # Check if training directory exists
    training_dir = "training/bisaya_vits"
    research_dir = "research_documentation"
    
    if not os.path.exists(training_dir):
        print(f"Training directory {training_dir} not found.")
        exit(1)
    
    # Find log file
    log_file = os.path.join(training_dir, "log.txt")
    if not os.path.exists(log_file):
        print(f"Log file {log_file} not found.")
        exit(1)
    
    # Parse log file
    metrics = parse_log_file(log_file)
    if not metrics:
        print("Failed to parse metrics from log file.")
        exit(1)
    
    # Plot metrics and save to research directory
    output = plot_metrics(metrics, research_dir)
    
    print(f"Metrics documentation saved to {research_dir}")
    print(f"Summary report: {output['summary']}") 