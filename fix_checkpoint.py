import os
import torch
import arg<PERSON><PERSON>

def fix_checkpoint_embedding(checkpoint_path, output_path=None):
    """
    Fix the checkpoint by adjusting the embedding size to match the configuration.
    """
    if output_path is None:
        # If no output path is specified, create one based on the input
        dirname = os.path.dirname(checkpoint_path)
        basename = os.path.basename(checkpoint_path)
        output_path = os.path.join(dirname, f"fixed_{basename}")
    
    print(f"Loading checkpoint: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Get the embedding weight shape
    emb_weight = checkpoint['model']['text_encoder.emb.weight']
    original_shape = emb_weight.shape
    print(f"Original embedding shape: {original_shape}")
    
    # Create a new embedding with the target shape (179 x 192)
    target_shape = (179, original_shape[1])  # Keep the same embedding dimension
    
    # Create a new weight tensor with the target shape
    new_emb_weight = torch.zeros(target_shape)
    
    # Copy the original embeddings to the new tensor
    # (preserve as many of the original weights as possible)
    min_rows = min(original_shape[0], target_shape[0])
    new_emb_weight[:min_rows, :] = emb_weight[:min_rows, :]
    
    # If the new embedding is larger, initialize the new rows
    # (typically with a mean or small random values)
    if target_shape[0] > original_shape[0]:
        # Initialize with the mean of existing embeddings
        mean_emb = emb_weight.mean(dim=0)
        for i in range(original_shape[0], target_shape[0]):
            new_emb_weight[i] = mean_emb + torch.randn_like(mean_emb) * 0.01  # Small random variation
    
    # Update the checkpoint with the new embedding
    checkpoint['model']['text_encoder.emb.weight'] = new_emb_weight
    
    print(f"New embedding shape: {new_emb_weight.shape}")
    print(f"Saving fixed checkpoint to: {output_path}")
    
    # Save the modified checkpoint
    torch.save(checkpoint, output_path)
    print("Checkpoint fixed successfully!")
    
    return output_path

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fix model checkpoint embeddings for VITS")
    parser.add_argument("--checkpoint", type=str, required=True, help="Path to the checkpoint file")
    parser.add_argument("--output", type=str, help="Path to save the fixed checkpoint (default: 'fixed_' prefix)")
    
    args = parser.parse_args()
    fix_checkpoint_embedding(args.checkpoint, args.output) 