name: "Docker build and push"
on:
  pull_request:
  push:
    branches:
      - main
      - dev
    tags:
      - v*
jobs:
  docker-build:
    name: "Build and push Docker image"
    runs-on: ubuntu-20.04
    strategy:
      matrix:
        arch: ["amd64"]
        base:
        - "nvidia/cuda:11.8.0-base-ubuntu22.04" # GPU enabled
        - "python:3.10.8-slim" # CPU only
    steps:
      - uses: actions/checkout@v2
      - name: Log in to the Container registry
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Compute Docker tags, check VERSION file matches tag
        id: compute-tag
        run: |
          set -ex
          base="ghcr.io/coqui-ai/tts"
          tags="" # PR build

          if [[ ${{ matrix.base }} = "python:3.10.8-slim" ]]; then
            base="ghcr.io/coqui-ai/tts-cpu"
          fi

          if [[ "${{ startsWith(github.ref, 'refs/heads/') }}" = "true" ]]; then
            # Push to branch
            github_ref="${{ github.ref }}"
            branch=${github_ref#*refs/heads/} # strip prefix to get branch name
            tags="${base}:${branch},${base}:${{ github.sha }},"
          elif [[ "${{ startsWith(github.ref, 'refs/tags/') }}" = "true" ]]; then
            VERSION="v$(cat TTS/VERSION)"
            if [[ "${{ github.ref }}" != "refs/tags/${VERSION}" ]]; then
              echo "Pushed tag does not match VERSION file. Aborting push."
              exit 1
            fi
            tags="${base}:${VERSION},${base}:latest,${base}:${{ github.sha }}"
          fi
          echo "::set-output name=tags::${tags}"
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1
      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: .
          platforms: linux/${{ matrix.arch }}
          push: ${{ github.event_name == 'push' }}
          build-args: "BASE=${{ matrix.base }}"
          tags: ${{ steps.compute-tag.outputs.tags }}
