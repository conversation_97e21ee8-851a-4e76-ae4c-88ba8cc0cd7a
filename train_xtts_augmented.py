import os
import json
import torch
import numpy as np
from trainer import Trainer
from TTS.utils.audio import AudioProcessor
from TTS.tts.datasets import load_tts_samples
from TTS.utils.synthesizer import Synthesizer
import matplotlib.pyplot as plt
from datetime import datetime
import pandas as pd
from jiwer import wer
import librosa
import soundfile as sf

class BisayaTTSTrainer:
    def __init__(self, config_path="config.json"):
        self.config = self.load_config(config_path)
        self.setup_directories()
        self.setup_training()
        
    def load_config(self, config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Update config for augmented dataset
        config["datasets"][0]["path"] = "data/wavs_augmented"
        config["datasets"][0]["meta_file_train"] = "metadata_augmented.csv"
        config["datasets"][0]["meta_file_val"] = "metadata_augmented.csv"
        
        return config
    
    def setup_directories(self):
        self.output_dir = "training_output_augmented"
        self.eval_dir = "evaluation_augmented"
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.eval_dir, exist_ok=True)
    
    def create_augmented_metadata(self):
        # Load original metadata
        df_orig = pd.read_csv("metadata.csv")
        
        # Create augmented metadata
        augmented_data = []
        for _, row in df_orig.iterrows():
            base_name = row['file_name'].replace('.wav', '')
            augmented_data.append({
                'file_name': row['file_name'],
                'text': row['text'],
                'speaker_name': row['speaker_name']
            })
            
            # Add augmented variations
            for aug_type in ['pitch', 'time', 'speed']:
                for value in ['3', '-3'] if aug_type == 'pitch' else ['0.8', '1.2']:
                    aug_file = f"{base_name}_{aug_type}_{value}.wav"
                    if os.path.exists(os.path.join("data/wavs_augmented", aug_file)):
                        augmented_data.append({
                            'file_name': aug_file,
                            'text': row['text'],
                            'speaker_name': row['speaker_name']
                        })
        
        # Save augmented metadata
        df_aug = pd.DataFrame(augmented_data)
        df_aug.to_csv("metadata_augmented.csv", index=False)
        print(f"Created augmented metadata with {len(df_aug)} entries")
    
    def setup_training(self):
        # Create augmented metadata
        self.create_augmented_metadata()
        
        # Initialize trainer
        self.trainer = Trainer(
            self.config,
            output_path=self.output_dir,
            train_samples=load_tts_samples(
                self.config["datasets"][0]["path"],
                self.config["datasets"][0]["meta_file_train"],
                self.config["datasets"][0]["name"],
            ),
            eval_samples=load_tts_samples(
                self.config["datasets"][0]["path"],
                self.config["datasets"][0]["meta_file_val"],
                self.config["datasets"][0]["name"],
            ),
            training_assets={"audio_processor": AudioProcessor(**self.config.audio)},
        )
    
    def evaluate_model(self, model_path):
        """Evaluate model performance with various metrics"""
        # Load model
        tts = Synthesizer(
            model_path,
            self.config,
            use_cuda=torch.cuda.is_available()
        )
        
        # Test sentences
        test_sentences = [
            "Maayong buntag! Unsa imong ngalan?",
            "Ganahan ko maminaw ug musika.",
            "Daghan kaayong salamat."
        ]
        
        results = {
            'wer': [],
            'inference_time': [],
            'audio_quality': []
        }
        
        for text in test_sentences:
            # Generate speech
            start_time = datetime.now()
            outputs = tts.tts(text)
            inference_time = (datetime.now() - start_time).total_seconds()
            
            # Save audio
            output_path = os.path.join(self.eval_dir, f"test_{text[:20]}.wav")
            sf.write(output_path, outputs['wav'], outputs['sample_rate'])
            
            # Calculate metrics
            results['wer'].append(wer(text, outputs['text']))
            results['inference_time'].append(inference_time)
            
            # Audio quality metrics
            audio, sr = librosa.load(output_path, sr=None)
            results['audio_quality'].append({
                'duration': len(audio) / sr,
                'rms': np.sqrt(np.mean(audio**2)),
                'zero_crossings': np.sum(np.diff(np.signbit(audio)))
            })
        
        return results
    
    def plot_metrics(self, results):
        """Plot evaluation metrics"""
        # Plot WER
        plt.figure(figsize=(10, 6))
        plt.bar(range(len(results['wer'])), results['wer'])
        plt.title('Word Error Rate by Test Sentence')
        plt.xlabel('Test Sentence Index')
        plt.ylabel('WER')
        plt.savefig(os.path.join(self.eval_dir, 'wer_plot.png'))
        plt.close()
        
        # Plot inference time
        plt.figure(figsize=(10, 6))
        plt.bar(range(len(results['inference_time'])), results['inference_time'])
        plt.title('Inference Time by Test Sentence')
        plt.xlabel('Test Sentence Index')
        plt.ylabel('Time (seconds)')
        plt.savefig(os.path.join(self.eval_dir, 'inference_time_plot.png'))
        plt.close()
        
        # Plot audio quality metrics
        quality_df = pd.DataFrame(results['audio_quality'])
        plt.figure(figsize=(12, 6))
        quality_df.boxplot()
        plt.title('Audio Quality Metrics')
        plt.ylabel('Value')
        plt.savefig(os.path.join(self.eval_dir, 'audio_quality_plot.png'))
        plt.close()
    
    def train(self):
        """Train the model and evaluate performance"""
        # Start training
        self.trainer.fit()
        
        # Evaluate best model
        best_model_path = os.path.join(self.output_dir, "best_model.pth")
        results = self.evaluate_model(best_model_path)
        
        # Plot metrics
        self.plot_metrics(results)
        
        # Save results
        with open(os.path.join(self.eval_dir, 'evaluation_results.json'), 'w') as f:
            json.dump(results, f, indent=4)

if __name__ == "__main__":
    trainer = BisayaTTSTrainer()
    trainer.train() 