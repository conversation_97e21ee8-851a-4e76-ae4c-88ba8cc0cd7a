import os
import json
import torch
from TTS.utils.audio import AudioProcessor
from TTS.tts.models import setup_model
from TTS.config import load_config

def test_environment():
    print("Testing environment setup...")
    
    # Check CUDA availability
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    
    # Check config file
    print("\nChecking config.json...")
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        print("✓ config.json loaded successfully")
    except Exception as e:
        print(f"✗ Error loading config.json: {e}")
        return False
    
    # Check directories
    print("\nChecking directories...")
    required_dirs = ["data/wavs_augmented", "training_output_augmented", "evaluation_augmented"]
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ Directory exists: {dir_path}")
        else:
            print(f"✗ Directory missing: {dir_path}")
            return False
    
    # Test TTS model setup
    print("\nTesting TTS model setup...")
    try:
        config = load_config("config.json")
        model = setup_model(config)
        print("✓ TTS model setup successful")
    except Exception as e:
        print(f"✗ Error in TTS model setup: {e}")
        return False
    
    # Test audio processor
    print("\nTesting audio processor...")
    try:
        audio_processor = AudioProcessor(**config.audio)
        print("✓ Audio processor setup successful")
    except Exception as e:
        print(f"✗ Error in audio processor setup: {e}")
        return False
    
    print("\nAll tests completed successfully!")
    return True

if __name__ == "__main__":
    test_environment() 