WadaSNR/
.idea/
*.pyc
.DS_Store
./__init__.py
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
.static_storage/
.media/
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# vim
*.swp
*.swm
*.swn
*.swo

# pytorch models
*.pth
*.pth.tar
!dummy_speakers.pth
result/

# setup.py
version.py

# jupyter dummy files
core

# ignore local datasets
recipes/WIP/*
recipes/ljspeech/LJSpeech-1.1/*
recipes/vctk/VCTK/*
recipes/**/*.npy
recipes/**/*.json
VCTK-Corpus-removed-silence/*

# ignore training logs
trainer_*_log.txt

# files used internally for dev, test etc.
tests/outputs/*
tests/train_outputs/*
TODO.txt
.vscode/*
data/*
notebooks/data/*
TTS/tts/utils/monotonic_align/core.c
.vscode-upload.json
temp_build/*
events.out*
old_configs/*
model_importers/*
model_profiling/*
docs/source/TODO/*
.noseids
.dccache
log.txt
umap.png
*.out
SocialMedia.txt
output.wav
tts_output.wav
deps.json
speakers.json
internal/*
*_pitch.npy
*_phoneme.npy
wandb
depot/*
coqui_recipes/*
local_scripts/*
coqui_demos/*