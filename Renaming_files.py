import os

# Define the folder containing your audio files
input_folder = "data/wavs_augmented"  # Replace with your folder path

# Get a list of all files in the folder
files = os.listdir(input_folder)

# Filter only .wav files
wav_files = [f for f in files if f.endswith(".wav")]

# Sort files to ensure consistent numbering
wav_files.sort()

# Define the starting number
start_number = 1  # 020 in three-digit format

# Rename files with 000 format
for idx, filename in enumerate(wav_files):
    # Extract the part of the filename after the number (if any)
    parts = filename.split("_", 1)  # Split on the first underscore
    if len(parts) > 1:
        new_name = f"{start_number + idx:03d}_{parts[1]}"  # Adjust numbering and keep the rest
    else:
        new_name = f"{start_number + idx:03d}_{filename}"  # If no underscore, just add the number

    # Rename the file
    old_path = os.path.join(input_folder, filename)
    new_path = os.path.join(input_folder, new_name)
    os.rename(old_path, new_path)
    print(f"Renamed: {filename} -> {new_name}")

print("All files renamed successfully!")
