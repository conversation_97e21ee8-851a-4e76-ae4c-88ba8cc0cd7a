import os
import argparse
import torch

from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.vits import Vits, VitsAudioConfig
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor
from trainer import Trainer, TrainerArgs

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Continue training VITS model from checkpoint with best model saving')
    parser.add_argument('--checkpoint_dir', type=str, required=True, 
                        help='Directory containing the checkpoint (e.g., training/bisaya_vits/bisaya_vits-April-10-2025_01+57AM-10597ee)')
    parser.add_argument('--checkpoint_file', type=str, default=None,
                        help='Specific checkpoint file to load (e.g., checkpoint_30000.pth). If not specified, will use the latest checkpoint.')
    args = parser.parse_args()

    # Check for available GPU
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Define paths
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "training/bisaya_vits")
    dataset_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data/wavs_augmented")
    phoneme_cache_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "phoneme_cache")

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    os.makedirs(phoneme_cache_path, exist_ok=True)

    # Find the checkpoint file
    checkpoint_path = None
    if args.checkpoint_file:
        # Use the specified checkpoint file
        checkpoint_path = os.path.join(args.checkpoint_dir, args.checkpoint_file)
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
    else:
        # Find the latest checkpoint in the directory
        checkpoint_files = [f for f in os.listdir(args.checkpoint_dir) if f.startswith("checkpoint_") and f.endswith(".pth")]
        if not checkpoint_files:
            raise FileNotFoundError(f"No checkpoint files found in {args.checkpoint_dir}")
        
        # Sort by step number (extracted from filename)
        checkpoint_files.sort(key=lambda x: int(x.split("_")[1].split(".")[0]))
        checkpoint_path = os.path.join(args.checkpoint_dir, checkpoint_files[-1])
    
    print(f"Loading checkpoint from: {checkpoint_path}")

    # Define dataset config
    dataset_config = BaseDatasetConfig(
        formatter="ljspeech", 
        meta_file_train="metadata_ljspeech.csv", 
        path=dataset_path
    )

    # Define audio config
    audio_config = VitsAudioConfig(
        sample_rate=22050, 
        win_length=1024, 
        hop_length=256, 
        num_mels=80, 
        mel_fmin=0, 
        mel_fmax=None
    )

    # Define VITS configuration
    config = VitsConfig(
        audio=audio_config,
        run_name="bisaya_vits",
        run_description="VITS training for Bisaya language",
        batch_size=16 if device == "cuda" else 4,  # Larger batch size for GPU
        eval_batch_size=8 if device == "cuda" else 2,
        batch_group_size=5,
        num_loader_workers=4 if device == "cuda" else 2,
        num_eval_loader_workers=2 if device == "cuda" else 1,
        run_eval=True,
        test_delay_epochs=5,
        epochs=1000,
        text_cleaner="phoneme_cleaners",
        use_phonemes=True,
        phoneme_language="en-us",
        phoneme_cache_path=phoneme_cache_path,
        compute_input_seq_cache=True,
        print_step=25,
        print_eval=True,
        mixed_precision=True,
        output_path=output_path,
        datasets=[dataset_config],
        cudnn_benchmark=True if device == "cuda" else False,
        test_sentences=[
            ["Ang tubig sa suba kay klaro kaayo"],
            ["Gusto ko moadto sa dagat"],
            ["Dili nako masabtan"],
            ["Kumusta ka"]
        ]
    )

    # INITIALIZE THE AUDIO PROCESSOR
    ap = AudioProcessor.init_from_config(config)

    # INITIALIZE THE TOKENIZER
    tokenizer, config = TTSTokenizer.init_from_config(config)

    # LOAD DATA SAMPLES
    train_samples, eval_samples = load_tts_samples(
        dataset_config,
        eval_split=True,
        eval_split_max_size=config.eval_split_max_size,
        eval_split_size=config.eval_split_size,
    )

    # init model
    model = Vits(config, ap, tokenizer, speaker_manager=None)
    
    # Move model to appropriate device
    model = model.to(device)

    # Load checkpoint and extract training state
    print(f"Loading checkpoint from {checkpoint_path}")
    state_dict = torch.load(checkpoint_path, map_location=device)
    
    # Load model state
    model.load_state_dict(state_dict['model'])
    
    # Extract training state information
    current_epoch = state_dict.get('epoch', 0)
    global_step = state_dict.get('step', 0)
    optimizer_state = state_dict.get('optimizer', None)
    scaler_state = state_dict.get('scaler', None)
    
    # Calculate max steps based on dataset size and epochs
    steps_per_epoch = len(train_samples) // config.batch_size
    max_global_step = steps_per_epoch * 100  # 100 is the original total epochs
    
    print(f"\nResuming training from:")
    print(f"- Epoch: {current_epoch}")
    print(f"- Global step: {global_step}")
    print(f"- Max global step: {max_global_step}")
    
    if global_step >= max_global_step:
        print("\nTraining has already reached maximum global steps!")
        print(f"Current step {global_step} >= Max step {max_global_step}")
        print("No further training needed.")
        return

    # Initialize trainer arguments with best model saving parameters
    trainer_args = TrainerArgs()
    
    # Set all essential parameters for training continuation
    trainer_args.continue_path = args.checkpoint_dir
    trainer_args.run_name = "bisaya_vits"
    trainer_args.run_description = "VITS training for Bisaya language"
    trainer_args.output_path = output_path
    
    # Set the current epoch and step
    trainer_args.start_epoch = current_epoch
    trainer_args.global_step = global_step
    trainer_args.max_global_step = max_global_step  # Add max step limit
    
    # Checkpoint and evaluation settings
    trainer_args.save_step = 1000           # Save checkpoint every 1000 steps
    trainer_args.save_n_checkpoints = 5     # Keep the last 5 checkpoints
    trainer_args.save_best_after = 0        # Start saving best model immediately
    trainer_args.save_checkpoints = True    # Enable checkpoint saving
    trainer_args.run_eval = True           # Enable evaluation
    trainer_args.eval_step = 1000          # Run evaluation every 1000 steps
    
    # Training settings
    trainer_args.print_step = 25           # Print progress every 25 steps
    trainer_args.print_eval = True         # Print evaluation results
    trainer_args.mixed_precision = True     # Enable mixed precision training
    trainer_args.epochs = 100              # Match original training configuration of 100 epochs
    
    print("\nTraining settings:")
    print(f"- Starting from epoch: {current_epoch}")
    print(f"- Starting from step: {global_step}")
    print(f"- Remaining epochs: {trainer_args.epochs - current_epoch}")
    print(f"- Best model saving: Enabled (starts immediately)")
    print(f"- Evaluation interval: Every {trainer_args.eval_step} steps")
    print(f"- Checkpoint interval: Every {trainer_args.save_step} steps")
    print(f"- Checkpoints kept: {trainer_args.save_n_checkpoints}")
    print(f"- Mixed precision: {trainer_args.mixed_precision}")
    
    # Run the trainer
    trainer = Trainer(
        trainer_args,
        config,
        output_path,
        model=model,
        train_samples=train_samples,
        eval_samples=eval_samples
    )
    
    # Manually load optimizer and scaler states if available
    if optimizer_state is not None:
        try:
            trainer.optimizer.load_state_dict(optimizer_state)
            print("Loaded optimizer state")
        except Exception as e:
            print(f"Could not load optimizer state: {e}")
            
    if scaler_state is not None:
        try:
            if hasattr(trainer, 'scaler') and trainer.scaler is not None:
                trainer.scaler.load_state_dict(scaler_state)
                print("Loaded scaler state")
        except Exception as e:
            print(f"Could not load scaler state: {e}")
    
    trainer.fit()

if __name__ == "__main__":
    # Fix for Windows multiprocessing
    import multiprocessing
    multiprocessing.freeze_support()
    main() 