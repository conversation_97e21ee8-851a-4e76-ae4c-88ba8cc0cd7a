import os
import argparse
import torch
import torch.cuda.amp as amp
import pandas as pd
import shutil
from TTS.tts.configs.shared_configs import BaseDatasetConfig
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models.vits import Vits, VitsAudioConfig
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from TTS.utils.audio import AudioProcessor
from trainer import Trainer, TrainerArgs

def setup_gpu():
    if not torch.cuda.is_available():
        print("\nNo GPU detected! Training will be on CPU which is much slower.\n")
        return False
    
    device = torch.cuda.get_device_name(0)
    print(f"\nGPU detected: {device}")
    print(f"CUDA Version: {torch.version.cuda}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
    print(f"GPU Memory Available: {torch.cuda.memory_allocated(0) / 1024**3:.2f} GB used")
    print(f"GPU Memory Cached: {torch.cuda.memory_reserved(0) / 1024**3:.2f} GB cached\n")
    return True

def setup_dataset(dataset_path):
    """Setup the dataset directory structure"""
    # Create wavs directory if it doesn't exist
    wavs_dir = os.path.join(dataset_path, "wavs")
    os.makedirs(wavs_dir, exist_ok=True)
    
    # Move audio files from audio directory to wavs directory
    audio_dir = os.path.join(dataset_path, "audio")
    if os.path.exists(audio_dir):
        for file in os.listdir(audio_dir):
            if file.endswith(".wav"):
                src = os.path.join(audio_dir, file)
                dst = os.path.join(wavs_dir, file)
                if not os.path.exists(dst):
                    shutil.copy2(src, dst)
        print(f"Copied audio files to {wavs_dir}")
    return wavs_dir

def convert_transcripts_to_ljspeech_format(dataset_path):
    """Convert the tab-separated transcripts.txt to LJSpeech format CSV"""
    transcripts_path = os.path.join(dataset_path, "transcripts.txt")
    output_path = os.path.join(dataset_path, "metadata_ljspeech.csv")
    
    # Read the tab-separated file
    with open(transcripts_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Convert to LJSpeech format (filename|transcription|normalized_transcription)
    with open(output_path, 'w', encoding='utf-8') as f:
        for line in lines:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                filename = parts[0].replace('.wav', '')
                transcription = parts[1]
                # Use the same text for normalized transcription
                f.write(f"{filename}|{transcription}|{transcription}\n")
    
    print(f"Converted transcripts to LJSpeech format: {output_path}")
    return output_path

def main():
    # Check GPU availability
    has_gpu = setup_gpu()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train VITS model')
    parser.add_argument('--continue_path', type=str, help='Path to checkpoint directory to resume training from')
    parser.add_argument('--pretrained_model_path', type=str, help='Path to pre-trained model weights for transfer learning')
    args = parser.parse_args()

    # Define paths
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "training/bisaya_vits_bloom")
    if args.continue_path:
        output_path = args.continue_path
        
    # Use the new Bloom Cebuano dataset
    dataset_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "/content/drive/MyDrive/Research_CS3B/BISAYA_TTS_VITS/data/bloom_ceb_dataset")
    phoneme_cache_path = os.path.join(output_path, "phoneme_cache")

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    os.makedirs(phoneme_cache_path, exist_ok=True)

    # Setup dataset directory structure
    setup_dataset(dataset_path)

    # Convert transcripts to LJSpeech format
    metadata_path = convert_transcripts_to_ljspeech_format(dataset_path)

    # Define dataset config for the Bloom Cebuano dataset
    dataset_config = BaseDatasetConfig(
        formatter="ljspeech", 
        meta_file_train="metadata_ljspeech.csv", 
        path=dataset_path
    )

    # Define audio config
    audio_config = VitsAudioConfig(
        sample_rate=22050, 
        win_length=1024, 
        hop_length=256, 
        num_mels=80, 
        mel_fmin=0, 
        mel_fmax=None
    )

    # Define VITS configuration with GPU settings
    config = VitsConfig(
        audio=audio_config,
        run_name="bisaya_vits_bloom",  # Changed name to indicate Bloom dataset
        run_description="VITS training for Bisaya language with Bloom Cebuano dataset",
        batch_size=32 if has_gpu else 4,  # Increased batch size for GPU
        eval_batch_size=16 if has_gpu else 2,
        batch_group_size=8 if has_gpu else 3,
        num_loader_workers=8 if has_gpu else 2,
        num_eval_loader_workers=4 if has_gpu else 1,
        run_eval=True,
        test_delay_epochs=5,
        epochs=100,
        text_cleaner="phoneme_cleaners",
        use_phonemes=True,
        phoneme_language="en-us",
        phoneme_cache_path=os.path.join(output_path, "phoneme_cache"),
        compute_input_seq_cache=True,
        print_step=5,
        print_eval=True,
        mixed_precision=False,  # Disabled mixed precision as requested
        output_path=output_path,
        datasets=[dataset_config],
        cudnn_benchmark=has_gpu,
        eval_split_size=0.02,
        eval_split_max_size=2,
        test_sentences=[
            ["Kumusta ka"],
            ["Maayong buntag"],
            ["Ang tubig sa suba kay klaro kaayo"],
            ["Gusto ko moadto sa dagat"]
        ]
    )

    # INITIALIZE THE AUDIO PROCESSOR
    ap = AudioProcessor.init_from_config(config)

    # INITIALIZE THE TOKENIZER
    tokenizer, config = TTSTokenizer.init_from_config(config)

    # LOAD DATA SAMPLES
    train_samples, eval_samples = load_tts_samples(
        dataset_config,
        eval_split=True,
        eval_split_max_size=config.eval_split_max_size,
        eval_split_size=config.eval_split_size,
    )

    # init model
    model = Vits(config, ap, tokenizer, speaker_manager=None)
    
    # Load pre-trained weights for transfer learning if specified
    if args.pretrained_model_path:
        print(f"Loading pre-trained model from: {args.pretrained_model_path}")
        try:
            checkpoint = torch.load(args.pretrained_model_path, map_location='cpu')
            
            # If it's a state dict with 'model' key
            if isinstance(checkpoint, dict) and 'model' in checkpoint:
                model.load_state_dict(checkpoint['model'], strict=False)
            # If it's just the model state dict directly
            else:
                model.load_state_dict(checkpoint, strict=False)
                
            print("Pre-trained model loaded successfully! Transfer learning enabled.")
        except Exception as e:
            print(f"Error loading pre-trained model: {e}")
            print("Continuing with model initialization from scratch.")
    
    # Move model to GPU and setup without mixed precision
    if has_gpu:
        model = model.cuda()
        print("Model moved to GPU successfully")

    # Configure trainer with GPU settings but without mixed precision
    trainer_args = TrainerArgs()
    trainer_args.mixed_precision = False  # Disabled mixed precision as requested
    trainer_args.num_loader_workers = 8 if has_gpu else 2
    trainer_args.num_eval_loader_workers = 4 if has_gpu else 1
    trainer_args.batch_size = 32 if has_gpu else 4
    trainer_args.eval_batch_size = 16 if has_gpu else 2
    trainer_args.batch_group_size = 8 if has_gpu else 3
    
    if args.continue_path:
        trainer_args.continue_path = args.continue_path
        print(f"Resuming training from: {args.continue_path}")
        
    trainer = Trainer(
        trainer_args,
        config,
        output_path,
        model=model,
        train_samples=train_samples,
        eval_samples=eval_samples,
    )
    trainer.fit()

if __name__ == "__main__":
    # Fix for Windows multiprocessing
    import multiprocessing
    multiprocessing.freeze_support()
    main() 