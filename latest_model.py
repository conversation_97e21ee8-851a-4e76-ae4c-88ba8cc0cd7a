import os
import argparse
import torch
import glob

def main():
    parser = argparse.ArgumentParser(description='Find the latest checkpoint and save it as latest_model.pth')
    parser.add_argument('--checkpoint_dir', type=str, required=True,
                      help='Directory containing the checkpoints')
    args = parser.parse_args()

    # Find all checkpoint files
    checkpoint_files = glob.glob(os.path.join(args.checkpoint_dir, "checkpoint_*.pth"))
    
    if not checkpoint_files:
        print("No checkpoint files found!")
        return
    
    # Sort by step number (extracted from filename)
    checkpoint_files.sort(key=lambda x: int(x.split("_")[-1].split(".")[0]))
    
    # Get the latest checkpoint
    latest_checkpoint = checkpoint_files[-1]
    print(f"Latest checkpoint: {latest_checkpoint}")
    
    # Load the checkpoint
    checkpoint = torch.load(latest_checkpoint, map_location=torch.device('cpu'))
    
    # Save as latest_model.pth
    latest_model_path = os.path.join(args.checkpoint_dir, "latest_model.pth")
    torch.save(checkpoint, latest_model_path)
    print(f"Saved latest model to {latest_model_path}")

if __name__ == "__main__":
    main() 