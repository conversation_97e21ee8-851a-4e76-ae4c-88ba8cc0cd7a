{"run_name": "bisaya_vits", "run_description": "VITS training for Bisaya language", "output_path": "./training/", "logger_uri": null, "model": "vits", "num_batch_buckets": 5, "batch_size": 16, "eval_batch_size": 8, "num_loader_workers": 4, "num_eval_loader_workers": 4, "batch_group_size": 5, "compute_input_seq_cache": true, "precompute_num_workers": 4, "epochs": 100, "save_step": 1000, "eval_step": 1000, "print_step": 100, "use_audio_input": false, "output_data_path": "output/training/", "optimizer": "RAdam", "optimizer_params": {"betas": [0.9, 0.98], "weight_decay": 0.0}, "lr_gen": 0.001, "lr_disc": 0.001, "lr_scheduler_gen": "ExponentialLR", "lr_scheduler_gen_params": {"gamma": 0.999875}, "lr_scheduler_disc": "ExponentialLR", "lr_scheduler_disc_params": {"gamma": 0.999875}, "scheduler_after_epoch": false, "grad_clip": 5.0, "min_text_len": 1, "max_text_len": 200, "min_audio_len": 1, "max_audio_len": 15, "datasets": [{"formatter": "l<PERSON><PERSON><PERSON><PERSON>", "dataset_name": "bisaya", "path": "./data/wavs_augmented", "meta_file_train": "metadata_ljspeech.csv", "meta_file_val": "metadata_ljspeech.csv"}], "dataset_configs": {"bisaya_dataset": {"meta_file_train": null, "meta_file_val": null, "path_is_metafile": true, "data_path": "data/wavs_augmented/wavs"}}, "audio": {"sample_rate": 22050, "fft_size": 1024, "win_length": 1024, "hop_length": 256, "num_mels": 80, "mel_fmin": 0, "mel_fmax": null}, "model_args": {"num_chars": 100, "out_channels": 513, "spec_segment_size": 32, "hidden_channels": 192, "hidden_channels_ffn_text_encoder": 768, "num_heads_text_encoder": 2, "num_layers_text_encoder": 6, "kernel_size_text_encoder": 3, "dropout_p_text_encoder": 0.1, "dropout_p_duration_predictor": 0.5, "kernel_size_posterior_encoder": 5, "dilation_rate_posterior_encoder": 1, "num_layers_posterior_encoder": 16, "kernel_size_flow": 5, "dilation_rate_flow": 1, "num_layers_flow": 4, "resblock_type_decoder": "1", "resblock_kernel_sizes_decoder": [3, 7, 11], "resblock_dilation_sizes_decoder": [[1, 3, 5], [1, 3, 5], [1, 3, 5]], "upsample_rates_decoder": [8, 8, 2, 2], "upsample_initial_channel_decoder": 512, "upsample_kernel_sizes_decoder": [16, 16, 4, 4], "use_sdp": true, "noise_scale": 1.0, "inference_noise_scale": 0.667, "length_scale": 1.0, "noise_scale_dp": 1.0, "inference_noise_scale_dp": 1.0, "max_inference_len": null, "init_discriminator": true, "use_spectral_norm_disriminator": false}, "kl_loss_alpha": 1.0, "disc_loss_alpha": 1.0, "gen_loss_alpha": 1.0, "feat_loss_alpha": 1.0, "mel_loss_alpha": 45.0, "dur_loss_alpha": 1.0, "speaker_encoder_loss_alpha": 1.0, "run_eval": true, "save_best_after": 10000, "text_cleaner": "phoneme_cleaners", "use_phonemes": false, "phoneme_language": "en-us", "phoneme_cache_path": "./phoneme_cache", "compute_f0": false, "compute_energy": false, "r": 1, "add_blank": true, "mixed_precision": false, "start_by_longest": true, "shuffle": true, "weighted_sampler": false, "verbose": true, "eval_split_size": 0.05, "test_delay_epochs": 5, "text_encoder_loss_alpha": 1, "use_speaker_embedding": false, "use_d_vector_file": false, "d_vector_dim": 0, "lr_scheduler": "NoamLR", "lr_scheduler_params": {"warmup_steps": 4000}, "lr": 0.001, "wd": 0.0, "test_sentences": ["Ang tubig sa suba kay klaro kaayo, ug makita nimo ang mga isda nga naglangoy", "<PERSON>to ko moadto sa dagat ug maglangoy sa limpyo nga tubig", "Nagtuon ko og matematika kay gusto nako masabtan ang mga numero", "Nganong wala man gyud ka nikuha sa imong cellphone nga gisulti nako nga tawagan tika", "<PERSON><PERSON> nako masabtan nganong nagsige ka og kasuko sa imong manghud nga gagmay pa kaayo"]}