# Based on XTTS v2 defaults
model: "tts_models/multilingual/multi-dataset/xtts_v2"
dataset_path: "data"
output_path: "bisaya_xtts_v2"
num_epochs: 50
batch_size: 16
gradient_accumulation_steps: 2
language: "bis"

# Dataset configuration
datasets:
  - meta_file_train: "data/metadata_xtts.csv"
    path: "data"
    text_column: "text"
    speaker_name_column: "speaker_name"
    file_name_column: "file_path"

# Training configuration
train:
  run_eval: false
  eval_interval: 1000
  save_interval: 1000
  save_strategy: "epoch"
  mixed_precision: true
  grad_clip: 1.0
  grad_clip_thresh: 1.0
  grad_clip_norm_type: 2
  optimizer: "AdamW"
  optimizer_params:
    lr: 1e-4
    betas: [0.8, 0.99]
    eps: 1e-9
    weight_decay: 0.01
  scheduler: "CosineAnnealingLR"
  scheduler_params:
    T_max: 1000
    eta_min: 1e-6