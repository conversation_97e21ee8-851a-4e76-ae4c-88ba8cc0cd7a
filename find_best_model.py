import os
import glob
import json
import torch
import torch.nn.functional as F
from TTS.tts.models.vits import Vits
from TTS.tts.configs.vits_config import VitsConfig
from TTS.utils.audio import AudioProcessor

def get_loss_value(loss_dict):
    """Extract a single loss value from a loss dictionary."""
    if isinstance(loss_dict, (int, float)):
        return float(loss_dict)
    elif isinstance(loss_dict, dict):
        # If it's a dictionary, sum all numeric values
        total = 0.0
        for value in loss_dict.values():
            if isinstance(value, (int, float)):
                total += float(value)
            elif isinstance(value, dict):
                total += get_loss_value(value)
        return total
    return float('inf')

def evaluate_checkpoint(checkpoint_path, config_path):
    """Evaluate a single checkpoint."""
    print(f"\nEvaluating checkpoint: {checkpoint_path}")
    
    # Initialize components
    vits_config = VitsConfig()
    vits_config.load_json(config_path)
    ap = AudioProcessor.init_from_config(vits_config)
    model = Vits(vits_config, ap)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=torch.device("cpu"))
    
    # Calculate total model parameters
    total_params = sum(p.numel() for p in model.parameters())
    
    # Load state dict and get training stats
    model.load_state_dict(checkpoint["model"])
    step = checkpoint.get("step", 0)
    epoch = checkpoint.get("epoch", 0)
    model_loss = checkpoint.get("model_loss", {})
    
    # Get train and eval losses
    train_loss = model_loss.get("train_loss", float('inf')) if isinstance(model_loss, dict) else float('inf')
    eval_loss = model_loss.get("eval_loss", None) if isinstance(model_loss, dict) else None
    
    metrics = {
        "step": step,
        "epoch": epoch,
        "train_loss": train_loss,
        "eval_loss": eval_loss,
        "total_params": total_params,
    }
    
    print(f"\nMetrics for {os.path.basename(checkpoint_path)}:")
    for metric, value in metrics.items():
        if isinstance(value, float):
            print(f"  {metric}: {value:.4f}")
        else:
            print(f"  {metric}: {value}")
    
    return metrics

def main():
    """Main function to find the best model from checkpoints."""
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint_dir", required=True, help="Directory containing checkpoints")
    parser.add_argument("--config_path", required=True, help="Path to config.json")
    args = parser.parse_args()
    
    # Find all checkpoint files
    checkpoint_pattern = os.path.join(args.checkpoint_dir, "checkpoint_*.pth")
    checkpoint_files = glob.glob(checkpoint_pattern)
    
    if not checkpoint_files:
        print(f"No checkpoint files found in {args.checkpoint_dir}")
        return
    
    print(f"Found {len(checkpoint_files)} checkpoints")
    
    # Evaluate each checkpoint
    best_checkpoint = None
    best_metrics = None
    best_loss = float('inf')
    
    metrics_summary = []
    
    for checkpoint_path in sorted(checkpoint_files):
        try:
            metrics = evaluate_checkpoint(checkpoint_path, args.config_path)
            metrics_summary.append({
                "checkpoint": os.path.basename(checkpoint_path),
                "metrics": metrics
            })
            
            if metrics["train_loss"] < best_loss:
                best_loss = metrics["train_loss"]
                best_checkpoint = checkpoint_path
                best_metrics = metrics
        except Exception as e:
            print(f"Error evaluating {checkpoint_path}: {str(e)}")
            continue
    
    if best_checkpoint:
        print(f"\nBest checkpoint: {os.path.basename(best_checkpoint)}")
        print("Best metrics:")
        for metric, value in best_metrics.items():
            if isinstance(value, float):
                print(f"  {metric}: {value:.4f}")
            else:
                print(f"  {metric}: {value}")
        
        # Save best model
        best_model_path = os.path.join(args.checkpoint_dir, "best_model.pth")
        import shutil
        shutil.copy2(best_checkpoint, best_model_path)
        print(f"\nSaved best model to: {best_model_path}")
        
        # Save metrics summary
        summary_path = os.path.join(args.checkpoint_dir, "checkpoint_metrics_summary.txt")
        with open(summary_path, "w", encoding="utf-8") as f:
            f.write("Checkpoint Evaluation Summary\n")
            f.write("===========================\n\n")
            for entry in metrics_summary:
                f.write(f"Checkpoint: {entry['checkpoint']}\n")
                for metric, value in entry['metrics'].items():
                    if isinstance(value, float):
                        f.write(f"  {metric}: {value:.4f}\n")
                    else:
                        f.write(f"  {metric}: {value}\n")
                f.write("\n")
        print(f"Saved metrics summary to: {summary_path}")
    else:
        print("No valid checkpoints found")

if __name__ == "__main__":
    main() 