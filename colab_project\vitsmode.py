
# Import necessary libraries
from TTS.utils.manage import ModelManager
import os
import glob
import shutil

# Create model manager
manager = ModelManager()

# Download the LJSpeech VITS model
print("Downloading LJSpeech VITS model...")
model_name = "tts_models/en/ljspeech/vits"
model_path = manager.download_model(model_name)
print(f"Model downloaded to: {model_path}")

# Find the model file
model_files = glob.glob(os.path.join(model_path, "*.pth"))
pretrained_model_path = None

if model_files:
    pretrained_model_path = model_files[0]
    print(f"Found model file: {pretrained_model_path}")
else:
    # Try looking for best_model.pth
    pretrained_model_path = os.path.join(model_path, "best_model.pth")
    if os.path.exists(pretrained_model_path):
        print(f"Found model file: {pretrained_model_path}")
    else:
        print("Could not find model file. Checking alternative locations...")
        # Check if there's a model in the pytorch_model.bin format
        alt_model_path = os.path.join(model_path, "pytorch_model.bin")
        if os.path.exists(alt_model_path):
            pretrained_model_path = alt_model_path
            print(f"Found alternative model file: {pretrained_model_path}")

# Copy the model file to your project directory
if pretrained_model_path:
    # Create a directory to store the pre-trained model
    project_dir = "/content/drive/MyDrive/Research_CS3B/BISAYA_TTS_VITS"
    pretrained_models_dir = os.path.join(project_dir, "pretrained_models")
    os.makedirs(pretrained_models_dir, exist_ok=True)
    
    # Copy the model file
    destination_path = os.path.join(pretrained_models_dir, "ljspeech_vits_pretrained.pth")
    shutil.copy2(pretrained_model_path, destination_path)
    print(f"Model file copied to: {destination_path}")
    
    # Print the command to use for training
    print("\n" + "="*50)
    print("To use this pre-trained model for transfer learning, run:")
    print(f"python train_vits.py --pretrained_model_path {destination_path}")
    print("="*50)
else:
    print("Failed to find a valid model file. Please check the model path manually.")

# Print information about the model
print("\nModel Information:")
print(f"Model type: VITS (Variational Inference with adversarial learning for end-to-end Text-to-Speech)")
print(f"Training dataset: LJSpeech (English single female speaker)")
print(f"Features: End-to-end TTS model capable of high-quality speech synthesis")
print(f"Original language: English")
print(f"Target language: Bisaya (for transfer learning)")