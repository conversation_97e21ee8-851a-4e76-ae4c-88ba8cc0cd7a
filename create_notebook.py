import json

# Create a minimal valid notebook structure
notebook = {
    "cells": [
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": ["# Bisaya Text-to-Speech Training"]
        },
        {
            "cell_type": "code",
            "execution_count": None,
            "metadata": {},
            "outputs": [],
            "source": ["# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')"]
        },
        {
            "cell_type": "code",
            "execution_count": None,
            "metadata": {},
            "outputs": [],
            "source": ["# Check if GPU is available\n", "import torch\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")"]
        },
        {
            "cell_type": "code",
            "execution_count": None,
            "metadata": {},
            "outputs": [],
            "source": ["# Verify dataset\n", "import os\n", "\n", "def verify_upload():\n", "    # Set workspace directory\n", "    WORKSPACE_DIR = \"/content/drive/MyDrive/Bisaya_TTS\"  # Change this to your actual path\n", "    \n", "    # Check main directories\n", "    print(\"Checking directory structure...\")\n", "    required_dirs = [\n", "        \"data/bloom_ceb_dataset\",\n", "        \"data/bloom_ceb_dataset/train\",\n", "        \"data/bloom_ceb_dataset/train/audio\",\n", "        \"data/bloom_ceb_dataset/validation\",\n", "        \"data/bloom_ceb_dataset/validation/audio\",\n", "        \"data/bloom_ceb_dataset/test\",\n", "        \"data/bloom_ceb_dataset/test/audio\",\n", "        \"formatters\"\n", "    ]\n", "    \n", "    for dir_path in required_dirs:\n", "        full_path = os.path.join(WORKSPACE_DIR, dir_path)\n", "        if os.path.exists(full_path):\n", "            print(f\"✓ {dir_path} exists\")\n", "        else:\n", "            print(f\"✗ {dir_path} missing!\")\n", "    \n", "    # Check critical files\n", "    print(\"\\nChecking critical files...\")\n", "    required_files = [\n", "        \"data/bloom_ceb_dataset/train/transcripts.txt\",\n", "        \"data/bloom_ceb_dataset/validation/transcripts.txt\",\n", "        \"data/bloom_ceb_dataset/test/transcripts.txt\",\n", "        \"formatters/bloom.py\",\n", "        \"train_vits.py\",\n", "        \"requirements.txt\"\n", "    ]\n", "    \n", "    for file_path in required_files:\n", "        full_path = os.path.join(WORKSPACE_DIR, file_path)\n", "        if os.path.exists(full_path):\n", "            print(f\"✓ {file_path} exists\")\n", "        else:\n", "            print(f\"✗ {file_path} missing!\")\n", "\n", "verify_upload()"]
        },
        {
            "cell_type": "code",
            "execution_count": None,
            "metadata": {},
            "outputs": [],
            "source": ["# Train model\n", "!python train_vits.py"]
        }
    ],
    "metadata": {
        "kernelspec": {
            "display_name": "Python 3",
            "language": "python",
            "name": "python3"
        },
        "language_info": {
            "codemirror_mode": {
                "name": "ipython",
                "version": 3
            },
            "file_extension": ".py",
            "mimetype": "text/x-python",
            "name": "python",
            "nbconvert_exporter": "python",
            "pygments_lexer": "ipython3",
            "version": "3.10.11"
        },
        "accelerator": "GPU",
        "colab": {
            "name": "bisaya_tts_colab.ipynb",
            "provenance": []
        }
    },
    "nbformat": 4,
    "nbformat_minor": 4
}

# Save the notebook to a file
with open("bisaya_tts_colab.ipynb", "w") as f:
    json.dump(notebook, f, indent=1)

print("Notebook created successfully!") 