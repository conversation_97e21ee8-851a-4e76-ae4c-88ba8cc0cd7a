import librosa
import librosa.display
import matplotlib.pyplot as plt
import numpy as np
import os
from pathlib import Path

def plot_audio_comparison(original_path, augmented_path, title):
    # Load audio files
    y_orig, sr_orig = librosa.load(original_path, sr=None)
    y_aug, sr_aug = librosa.load(augmented_path, sr=None)
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # Plot waveforms
    librosa.display.waveshow(y_orig, sr=sr_orig, ax=ax1)
    ax1.set_title(f'Original: {Path(original_path).name}')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Amplitude')
    
    librosa.display.waveshow(y_aug, sr=sr_aug, ax=ax2)
    ax2.set_title(f'Augmented: {Path(augmented_path).name}')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Amplitude')
    
    # Plot spectrograms
    fig, (ax3, ax4) = plt.subplots(2, 1, figsize=(12, 8))
    
    D_orig = librosa.amplitude_to_db(np.abs(librosa.stft(y_orig)), ref=np.max)
    D_aug = librosa.amplitude_to_db(np.abs(librosa.stft(y_aug)), ref=np.max)
    
    librosa.display.specshow(D_orig, y_axis='log', ax=ax3)
    ax3.set_title('Original Spectrogram')
    ax3.set_xlabel('Time (s)')
    ax3.set_ylabel('Frequency (Hz)')
    
    librosa.display.specshow(D_aug, y_axis='log', ax=ax4)
    ax4.set_title('Augmented Spectrogram')
    ax4.set_xlabel('Time (s)')
    ax4.set_ylabel('Frequency (Hz)')
    
    plt.tight_layout()
    plt.savefig(f'augmentation_visualization_{title}.png')
    plt.close()

def analyze_augmentation():
    # Example file to analyze
    base_file = "data/wavs_resampled/unsay_imong_ganahan_nga_adlaw_sa_semana.wav"
    
    # Create visualization directory
    os.makedirs('augmentation_visualizations', exist_ok=True)
    
    # Analyze different augmentation types
    augmentations = {
        'pitch': ['pitch_3', 'pitch_-3'],
        'time': ['time_0.8', 'time_1.2'],
        'speed': ['speed_0.8', 'speed_1.2']
    }
    
    for aug_type, variations in augmentations.items():
        for var in variations:
            aug_file = f"data/wavs_augmented/unsay_imong_ganahan_nga_adlaw_sa_semana_{var}.wav"
            if os.path.exists(aug_file):
                plot_audio_comparison(base_file, aug_file, f"{aug_type}_{var}")

if __name__ == "__main__":
    analyze_augmentation() 