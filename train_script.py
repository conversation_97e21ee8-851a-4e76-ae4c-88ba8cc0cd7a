import os
import torch
import json
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.models.vits import Vits
from TTS.utils.audio import AudioProcessor
from TTS.tts.datasets.dataset import TTSDataset
from TTS.tts.utils.text.tokenizer import TTSTokenizer
from trainer.trainer import Trainer, TrainerArgs

# Load config
with open("config.json", "r", encoding="utf-8") as f:
    config_dict = json.load(f)

# Create output dir
os.makedirs(config_dict["output_path"], exist_ok=True)

# Initialize VITS config
config = VitsConfig()
# Load config from dict
for key, value in config_dict.items():
    if hasattr(config, key):
        setattr(config, key, value)

# Load training samples
print("Loading training data...")
train_samples = []
eval_samples = []

# Since we have problems with the default dataset loader, we'll do it manually
metadata_path = "data/wavs_augmented/metadata_ljspeech.csv"
wavs_path = "data/wavs_augmented/wavs"
root_path = "data/wavs_augmented"

with open(metadata_path, "r", encoding="utf-8") as f:
    lines = f.readlines()
    
samples = []
for line in lines:
    parts = line.strip().split("|")
    file_name = parts[0]
    text = parts[1]
    audio_path = os.path.join(wavs_path, file_name + ".wav")
    if os.path.exists(audio_path):
        samples.append({
            "text": text,
            "audio_file": audio_path,
            "speaker_name": "bisaya",
            "language": "bisaya",
            "root_path": root_path,
            "audio_unique_name": file_name
        })

# Split into train and eval
split_index = int(len(samples) * 0.95)  # 95% for training
train_samples = samples[:split_index]
eval_samples = samples[split_index:]

print(f"Using {len(train_samples)} samples for training")
print(f"Using {len(eval_samples)} samples for evaluation")

# Create tokenizer
tokenizer, _ = TTSTokenizer.init_from_config(config)

# Create audio processor
ap = AudioProcessor.init_from_config(config)

# Init datasets
train_dataset = TTSDataset(
    samples=train_samples,
    tokenizer=tokenizer,
    ap=ap,
    batch_group_size=config.batch_size,
    min_text_len=config.get("min_text_len", 1),
    max_text_len=config.get("max_text_len", 190),
    min_audio_len=config.get("min_audio_len", 1),
    max_audio_len=config.get("max_audio_len", 500000),
    phoneme_cache_path=config.get("phoneme_cache_path", None),
    precompute_num_workers=config.get("precompute_num_workers", 0),
    verbose=True,
    text_cleaner=config.get("text_cleaner", None),
    start_by_longest=config.get("start_by_longest", False),
    return_wav=True,
)

eval_dataset = TTSDataset(
    samples=eval_samples,
    tokenizer=tokenizer,
    ap=ap,
    batch_group_size=config.eval_batch_size,
    min_text_len=config.get("min_text_len", 1),
    max_text_len=config.get("max_text_len", 190),
    min_audio_len=config.get("min_audio_len", 1),
    max_audio_len=config.get("max_audio_len", 500000),
    phoneme_cache_path=config.get("phoneme_cache_path", None),
    verbose=True,
    text_cleaner=config.get("text_cleaner", None),
    start_by_longest=config.get("start_by_longest", False),
    return_wav=True,
)

# Init the model
model = Vits(config, ap, tokenizer, speaker_manager=None, language_manager=None)

# Init the trainer
trainer = Trainer(
    TrainerArgs(
        restore_path=None,
        skip_train_epoch=False,
        start_with_eval=False,
        grad_clip=config.grad_clip,
        print_step=50,
        save_step=1000,
        plot_step=100,
        trainer_type="tts",
        run_eval=True,
        batch_size=config.batch_size,
        eval_batch_size=config.eval_batch_size,
        mixed_precision=config.mixed_precision,
        epochs=config.epochs,
        output_path=config.output_path,
        log_directory=config.run_name,
        lr=config.lr_gen,
        lr_disc=config.lr_disc,
        wd=config_dict["optimizer_params"].get("weight_decay", 0.01),
        steps_to_start_discriminator=100
    ),
    config,
    output_path=config.output_path,
    model=model,
    train_samples=train_samples,
    eval_samples=eval_samples,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    training_assets=None,
    parse_command_line_args=False,
    printer=None
)

# Start training
trainer.fit() 