import pandas as pd
import matplotlib.pyplot as plt
import re
from datetime import datetime

# Function to parse the log file
def parse_log_file(file_path):
    steps = []
    losses_gen = []
    losses_disc = []
    losses_mel = []
    times = []
    
    current_step = None
    current_gen = None
    current_disc = None
    current_mel = None
    current_time = None
    
    with open(file_path, 'r') as f:
        for line in f:
            # Look for step information
            step_match = re.search(r'STEP: (\d+)/308', line)
            if step_match:
                if current_step is not None and current_gen is not None and current_disc is not None and current_mel is not None:
                    steps.append(current_step)
                    losses_gen.append(current_gen)
                    losses_disc.append(current_disc)
                    losses_mel.append(current_mel)
                    times.append(current_time)
                
                current_step = int(step_match.group(1))
                current_gen = None
                current_disc = None
                current_mel = None
                
                # Extract timestamp
                time_match = re.search(r'TIME: (.*?) --', line)
                if time_match:
                    time_str = time_match.group(1)
                    try:
                        current_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    except:
                        current_time = None
                        
            # Look for loss values
            if 'loss_gen:' in line:
                loss_match = re.search(r'loss_gen: ([\d.]+)', line)
                if loss_match:
                    current_gen = float(loss_match.group(1))
                    
            if 'loss_disc:' in line and 'loss_disc_real' not in line:
                loss_match = re.search(r'loss_disc: ([\d.]+)', line)
                if loss_match:
                    current_disc = float(loss_match.group(1))
                    
            if 'loss_mel:' in line:
                loss_match = re.search(r'loss_mel: ([\d.]+)', line)
                if loss_match:
                    current_mel = float(loss_match.group(1))
    
    # Create DataFrame
    df = pd.DataFrame({
        'step': steps,
        'loss_gen': losses_gen,
        'loss_disc': losses_disc,
        'loss_mel': losses_mel,
        'time': times
    })
    
    return df

# Parse the log file
df = parse_log_file('training/bisaya_vits/bisaya_vits_test-April-07-2025_08+11AM-898988d/trainer_0_log.txt')

# Create visualizations
plt.figure(figsize=(15, 10))

# Plot all losses on the same graph with different scales
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 15))

# Plot Generator and Discriminator Loss
ax1.plot(df['step'], df['loss_gen'], label='Generator Loss', color='blue')
ax1.plot(df['step'], df['loss_disc'], label='Discriminator Loss', color='red')
ax1.set_title('Generator and Discriminator Losses over Training Steps')
ax1.set_xlabel('Training Steps')
ax1.set_ylabel('Loss Value')
ax1.grid(True)
ax1.legend()

# Plot Mel-Spectrogram Loss
ax2.plot(df['step'], df['loss_mel'], label='Mel Loss', color='green')
ax2.set_title('Mel-Spectrogram Loss over Training Steps')
ax2.set_xlabel('Training Steps')
ax2.set_ylabel('Mel Loss')
ax2.grid(True)
ax2.legend()

plt.tight_layout()
plt.savefig('training_metrics.png')

# Print summary statistics
print("\nTraining Metrics Summary:")
print("-" * 50)

if not df.empty:
    print("\nGenerator Loss:")
    print(f"Initial: {df['loss_gen'].iloc[0]:.2f}")
    print(f"Final: {df['loss_gen'].iloc[-1]:.2f}")
    print(f"Improvement: {((df['loss_gen'].iloc[0] - df['loss_gen'].iloc[-1]) / df['loss_gen'].iloc[0] * 100):.2f}%")

    print("\nDiscriminator Loss:")
    print(f"Initial: {df['loss_disc'].iloc[0]:.2f}")
    print(f"Final: {df['loss_disc'].iloc[-1]:.2f}")
    print(f"Change: {((df['loss_disc'].iloc[0] - df['loss_disc'].iloc[-1]) / df['loss_disc'].iloc[0] * 100):.2f}%")

    print("\nMel-Spectrogram Loss:")
    print(f"Initial: {df['loss_mel'].iloc[0]:.2f}")
    print(f"Final: {df['loss_mel'].iloc[-1]:.2f}")
    print(f"Improvement: {((df['loss_mel'].iloc[0] - df['loss_mel'].iloc[-1]) / df['loss_mel'].iloc[0] * 100):.2f}%")

    # Calculate improvements per epoch
    print("\nAverage Loss Values per Epoch:")
    print("-" * 50)
    epoch_size = 308  # steps per epoch
    epochs = df['step'].max() // epoch_size + 1
    
    for epoch in range(epochs):
        epoch_data = df[(df['step'] >= epoch * epoch_size) & (df['step'] < (epoch + 1) * epoch_size)]
        if not epoch_data.empty:
            print(f"\nEpoch {epoch}:")
            print(f"Generator Loss: {epoch_data['loss_gen'].mean():.2f}")
            print(f"Discriminator Loss: {epoch_data['loss_disc'].mean():.2f}")
            print(f"Mel Loss: {epoch_data['loss_mel'].mean():.2f}")

# Save metrics to CSV
df.to_csv('training_metrics.csv', index=False)
print("\nMetrics saved to 'training_metrics.csv' and plot saved to 'training_metrics.png'") 