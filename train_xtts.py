import os
from TTS.trainer import Trainer
from TTS.config import load_config
from TTS.utils.audio import AudioProcessor
from TTS.tts.datasets import load_tts_samples
from TTS.tts.models import setup_model
from TTS.tts.utils.managers import load_checkpoint
from TTS.tts.utils.speakers import SpeakerManager
from TTS.utils.synthesizer import Synthesizer

# Paths
OUT_PATH = "training_output"
DATA_PATH = "data/wavs_resampled"
CONFIG_PATH = "config.json"

# Training configuration
config = {
    "run_name": "bisaya_xtts",
    "model": "tts_models/multilingual/multi-dataset/xtts_v2",
    "run_eval": True,
    "test_delay": -1,
    "epochs": 1000,
    "text_cleaner": "multilingual_cleaners",
    "use_phonemes": False,
    "phoneme_language": "en-us",
    "phoneme_cache_path": "phoneme_cache",
    "print_step": 25,
    "print_eval": True,
    "mixed_precision": True,
    "output_path": OUT_PATH,
    "datasets": [
        {
            "name": "bisaya_dataset",
            "path": DATA_PATH,
            "meta_file_train": "metadata.csv",
            "meta_file_val": "metadata.csv",
        }
    ],
    "batch_size": 16,
    "eval_batch_size": 16,
    "num_loader_workers": 4,
    "num_eval_loader_workers": 4,
    "run_eval": True,
    "test_delay": -1,
    "epochs": 1000,
    "text_cleaner": "multilingual_cleaners",
    "use_phonemes": False,
    "phoneme_language": "en-us",
    "phoneme_cache_path": "phoneme_cache",
    "print_step": 25,
    "print_eval": True,
    "mixed_precision": True,
    "output_path": OUT_PATH,
    "datasets": [
        {
            "name": "bisaya_dataset",
            "path": DATA_PATH,
            "meta_file_train": "metadata.csv",
            "meta_file_val": "metadata.csv",
        }
    ],
    "batch_size": 16,
    "eval_batch_size": 16,
    "num_loader_workers": 4,
    "num_eval_loader_workers": 4,
}

# Save config
import json
with open(CONFIG_PATH, 'w') as f:
    json.dump(config, f, indent=4)

# Initialize trainer
trainer = Trainer(
    config,
    output_path=OUT_PATH,
    train_samples=load_tts_samples(
        config["datasets"][0]["path"],
        config["datasets"][0]["meta_file_train"],
        config["datasets"][0]["name"],
    ),
    eval_samples=load_tts_samples(
        config["datasets"][0]["path"],
        config["datasets"][0]["meta_file_val"],
        config["datasets"][0]["name"],
    ),
    training_assets={"audio_processor": AudioProcessor(**config.audio)},
)

# Start training
trainer.fit()
