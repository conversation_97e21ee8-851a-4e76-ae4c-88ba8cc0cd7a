import os
import pandas as pd

# Define the main dataset folder
data_folder = "data"  # Update if needed
output_csv = os.path.join(data_folder, "metadata_combined_speakers.csv")  # Final combined CSV

# List of speaker CSV filenames
speaker_csv_files = [
    "metadata_speaker_1.csv",
    "metadata_speaker_2.csv",
    "metadata_speaker_3.csv",
    "metadata_speaker_4.csv"
]

# List to store DataFrames
dataframes = []

# Loop through each speaker CSV file
for csv_filename in speaker_csv_files:
    csv_path = os.path.join(data_folder, csv_filename)
    if os.path.exists(csv_path):  # Check if the CSV file exists
        df = pd.read_csv(csv_path)
        dataframes.append(df)
    else:
        print(f"Warning: {csv_path} not found. Skipping...")

# Concatenate all CSV files if any are found
if dataframes:
    combined_df = pd.concat(dataframes, ignore_index=True)
    combined_df.to_csv(output_csv, index=False)
    print(f"Combined CSV saved at: {output_csv}")
else:
    print("No CSV files found to combine.")
