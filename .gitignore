venv/
__pycache__/
*.py[cod]
*$py.class
.env
.venv
env/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.idea/
.vscode/
*.log
.DS_Store

# Project specific folders
conversion/
data/
augmentation_env/
wavs_1/
wavs_2/
wavs_3/
wav_4/
test_outputs/
output/
phoneme_cache/
training/
colab_project/MMS_TTS/

# Generated files
events.out.tfevents.*
training_metrics.csv
*.npy
*.pth
checkpoint_*
best_model*.pth

# Cache and temporary files
__pycache__/
.ipynb_checkpoints/
*.pyc
*.swp
*.swo
.pytest_cache/
.coverage
htmlcov/

# Additional environment folders
myenv/
