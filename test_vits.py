import os
import torch
from TTS.tts.configs.vits_config import VitsConfig
from TTS.tts.models.vits import Vits

# Load the config
config = VitsConfig()
config.load_json("training/bisaya_vits/bisaya_vits_test-April-07-2025_08+11AM-898988d/config.json")

# Initialize the model
model = Vits.init_from_config(config)

# Load the model weights
model.load_checkpoint(config, "training/bisaya_vits/bisaya_vits_test-April-07-2025_08+11AM-898988d/best_model.pth")

# Move model to CPU and set to evaluation mode
model.cpu()
model.eval()

# Create output directory if it doesn't exist
os.makedirs("test_outputs", exist_ok=True)

# Test sentences
test_sentences = [
    "Kumusta ka",
    "Maayong buntag",
    "Salamat kaayo"
]

# Generate audio for each test sentence
for idx, text in enumerate(test_sentences):
    print(f"Generating audio for: {text}")
    with torch.no_grad():
        outputs = model.infer(
            text=text,
            reference_wav=None,
            style_wav=None,
            language_id=None
        )
    
    # Save the audio file
    output_path = f"test_outputs/output_{idx}.wav"
    model.ap.save_wav(outputs["wav"], output_path)
    print(f"Saved to: {output_path}") 