import os
import librosa
import soundfile as sf
from pathlib import Path

def convert_to_16khz(input_dir, output_dir, target_sr=16000):
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Process each audio file
    for file_path in Path(input_dir).glob('*.wav'):
        # Load audio and get its original sample rate
        audio, sr = librosa.load(file_path, sr=None)
        print(f"File: {file_path.name}, Original Sample Rate: {sr} Hz")
        
        # Resample to 16kHz if not already
        if sr != target_sr:
            audio = librosa.resample(audio, orig_sr=sr, target_sr=target_sr)
        
        # Save the resampled audio
        output_path = Path(output_dir) / file_path.name
        sf.write(output_path, audio, target_sr)
        print(f"Saved 16kHz version to: {output_path}")

if __name__ == "__main__":
    input_directory = "data/wavs"  # Replace with your input directory
    output_directory = "data/wavs_resampled"  # Replace with your output directory
    
    convert_to_16khz(input_directory, output_directory)
    print("Resampling to 16kHz complete!")