from TTS.api import TTS

# Get device
device = "cpu"  # since we don't have GPU

# Initialize TTS with our custom model
tts = TTS(model_path="training/bisaya_vits/bisaya_vits_test-April-07-2025_08+11AM-898988d/best_model.pth", 
          config_path="training/bisaya_vits/bisaya_vits_test-April-07-2025_08+11AM-898988d/config.json",
          progress_bar=True,
          gpu=False)

# Test sentences
test_texts = [
    "Kumusta ka",
    "Maayong buntag",
    "Salamat kaayo"
]

# Generate audio for each test sentence
for idx, text in enumerate(test_texts):
    print(f"Generating audio for: {text}")
    # Generate audio
    tts.tts_to_file(text=text, 
                    file_path=f"test_outputs/output_{idx}.wav") 