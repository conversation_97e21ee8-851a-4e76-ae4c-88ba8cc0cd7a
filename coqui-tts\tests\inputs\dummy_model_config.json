{
    "run_name": "mozilla-no-loc-fattn-stopnet-sigmoid-loss_masking",
    "run_description": "using forward attention, with original prenet, loss masking,separate stopnet, sigmoid. Compare this with 4817. Pytorch DPP",

    "audio":{
        // Audio processing parameters
        "num_mels": 80,         // size of the mel spec frame.
        "fft_size": 1024,       // number of stft frequency levels. Size of the linear spectogram frame.
        "sample_rate": 22050,   // DATASET-RELATED: wav sample-rate. If different than the original data, it is resampled.
        "hop_length": 256,
        "win_length": 1024,
        "preemphasis": 0.98,    // pre-emphasis to reduce spec noise and make it more structured. If 0.0, no -pre-emphasis.
        "min_level_db": -100,   // normalization range
        "ref_level_db": 20,     // reference level db, theoretically 20db is the sound of air.
        "power": 1.5,           // value to sharpen wav signals after GL algorithm.
        "griffin_lim_iters": 60,// #griffin-lim iterations. 30-60 is a good range. Larger the value, slower the generation.
        // Normalization parameters
        "signal_norm": true,    // normalize the spec values in range [0, 1]
        "symmetric_norm": false, // move normalization to range [-1, 1]
        "max_norm": 1,          // scale normalization to range [-max_norm, max_norm] or [0, max_norm]
        "clip_norm": true,      // clip normalized values into the range.
        "mel_fmin": 0.0,         // minimum freq level for mel-spec. ~50 for male and ~95 for female voices. Tune for dataset!!
        "mel_fmax": 8000.0,        // maximum freq level for mel-spec. Tune for dataset!!
        "do_trim_silence": true  // enable trimming of slience of audio as you load it. LJspeech (false), TWEB (false), Nancy (true)
    },

    "distributed":{
        "backend": "nccl",
        "url": "tcp:\/\/localhost:54321"
    },

    "reinit_layers": [],

    "model": "Tacotron2",          // one of the model in models/
    "grad_clip": 1,                // upper limit for gradients for clipping.
    "epochs": 1000,                // total number of epochs to train.
    "lr": 0.0001,                  // Initial learning rate. If Noam decay is active, maximum learning rate.
    "lr_decay": false,             // if true, Noam learning rate decaying is applied through training.
    "warmup_steps": 4000,          // Noam decay steps to increase the learning rate from 0 to "lr"
    "windowing": false,            // Enables attention windowing. Used only in eval mode.
    "memory_size": 5,              // ONLY TACOTRON - memory queue size used to queue network predictions to feed autoregressive connection. Useful if r < 5.
    "attention_norm": "sigmoid",   // softmax or sigmoid. Suggested to use softmax for Tacotron2 and sigmoid for Tacotron.
    "prenet_type": "original",     // ONLY TACOTRON2 - "original" or "bn".
    "prenet_dropout": true,        // ONLY TACOTRON2 - enable/disable dropout at prenet.
    "use_forward_attn": true,      // ONLY TACOTRON2 - if it uses forward attention. In general, it aligns faster.
    "forward_attn_mask": false,
    "attention_type": "original",
    "attention_heads": 5,
    "bidirectional_decoder": false,
    "transition_agent": false,     // ONLY TACOTRON2 - enable/disable transition agent of forward attention.
    "location_attn": false,        // ONLY TACOTRON2 - enable_disable location sensitive attention. It is enabled for TACOTRON by default.
    "loss_masking": true,         // enable / disable loss masking against the sequence padding.
    "enable_eos_bos_chars": false, // enable/disable beginning of sentence and end of sentence chars.
    "stopnet": true,               // Train stopnet predicting the end of synthesis.
    "separate_stopnet": true,     // Train stopnet seperately if 'stopnet==true'. It prevents stopnet loss to influence the rest of the model. It causes a better model, but it trains SLOWER.
    "tb_model_param_stats": false,     // true, plots param stats per layer on tensorboard. Might be memory consuming, but good for debugging.
    "use_gst": false,
    "double_decoder_consistency": true,  // use DDC explained here https://erogol.com/solving-attention-problems-of-tts-models-with-double-decoder-consistency-draft/
    "ddc_r": 7,                           // reduction rate for coarse decoder.

    "batch_size": 32,       // Batch size for training. Lower values than 32 might cause hard to learn attention.
    "eval_batch_size":16,
    "r": 1,                 // Number of frames to predict for step.
    "wd": 0.000001,         // Weight decay weight.
    "checkpoint": true,     // If true, it saves checkpoints per "save_step"
    "save_step": 1000,      // Number of training steps expected to save traning stats and checkpoints.
    "print_step": 10,       // Number of steps to log traning on console.
    "batch_group_size": 0,  //Number of batches to shuffle after bucketing.

    "run_eval": true,
    "test_delay_epochs": 5,  //Until attention is aligned, testing only wastes computation time.
    "test_sentences_file": null,  // set a file to load sentences to be used for testing. If it is null then we use default english sentences.
    "data_path": "/media/erogol/data_ssd/Data/Mozilla/",  // DATASET-RELATED: can overwritten from command argument
    "meta_file_train": "metadata_train.txt",      // DATASET-RELATED: metafile for training dataloader.
    "meta_file_val": "metadata_val.txt",    // DATASET-RELATED: metafile for evaluation dataloader.
    "dataset": "mozilla",      // DATASET-RELATED: one of mozilla_voice_tts.dataset.preprocessors depending on your target dataset. Use "tts_cache" for pre-computed dataset by extract_features.py
    "min_seq_len": 0,       // DATASET-RELATED: minimum text length to use in training
    "max_seq_len": 150,     // DATASET-RELATED: maximum text length
    "output_path": "../keep/",      // DATASET-RELATED: output path for all training outputs.
    "num_loader_workers": 4,        // number of training data loader processes. Don't set it too big. 4-8 are good values.
    "num_val_loader_workers": 4,    // number of evaluation data loader processes.
    "phoneme_cache_path": "mozilla_us_phonemes",  // phoneme computation is slow, therefore, it caches results in the given folder.
    "use_phonemes": false,           // use phonemes instead of raw characters. It is suggested for better pronounciation.
    "phoneme_language": "en-us",     // depending on your target language, pick one from  https://github.com/bootphon/phonemizer#languages
    "text_cleaner": "phoneme_cleaners",
    "use_speaker_embedding": false, // whether to use additional embeddings for separate speakers

    // MULTI-SPEAKER and GST
    "use_speaker_embedding": false,     // use speaker embedding to enable multi-speaker learning.
    "gst":	{			                // gst parameter if gst is enabled
        "gst_style_input": null,        // Condition the style input either on a
                                        // -> wave file [path to wave] or
                                        // -> dictionary using the style tokens {'token1': 'value', 'token2': 'value'} example {"0": 0.15, "1": 0.15, "5": -0.15}
                                        // with the dictionary being len(dict) <= len(gst_style_tokens).
        "gst_use_speaker_embedding": true, // if true pass speaker embedding in attention input GST.
        "gst_embedding_dim": 512,
        "gst_num_heads": 4,
        "gst_style_tokens": 10
        }
}


